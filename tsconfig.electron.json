{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "dist/electron", "rootDir": "electron", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["electron/**/*"], "exclude": ["node_modules", "dist", "src"]}