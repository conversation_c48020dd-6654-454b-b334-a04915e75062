<template>
  <div class="ecg-display">
    <div class="controls">
      <div class="control-group">
        <label>数据源:</label>
        <select v-model="dataSource" @change="onDataSourceChange">
          <option value="file">CSV文件</option>
          <option value="sse">SSE推送</option>
        </select>
      </div>
      
      <div class="control-group" v-if="dataSource === 'file'">
        <button @click="loadCsvFile" :disabled="isLoading">
          {{ isLoading ? '加载中...' : '加载CSV文件' }}
        </button>
      </div>
      
      <div class="control-group" v-if="dataSource === 'sse'">
        <input 
          v-model="sseUrl" 
          placeholder="SSE URL" 
          :disabled="isConnected"
        />
        <button @click="toggleSSE" :disabled="isLoading">
          {{ isConnected ? '断开连接' : '连接SSE' }}
        </button>
      </div>
      
      <div class="control-group">
        <label>走纸速度:</label>
        <select v-model="speedMultiplier" @change="updateRenderConfig">
          <option :value="0.25">12.5mm/s</option>
          <option :value="0.5">25mm/s</option>
          <option :value="1">50mm/s</option>
          <option :value="2">100mm/s</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>电压倍数:</label>
        <select v-model="voltageMultiplier" @change="updateRenderConfig">
          <option :value="0.25">x0.25</option>
          <option :value="0.5">x0.5</option>
          <option :value="1">x1</option>
          <option :value="2">x2</option>
        </select>
      </div>
      
      <div class="control-group">
        <button @click="clearDisplay">清空显示</button>
      </div>
      
      <div class="status">
        <span>状态: {{ status }}</span>
        <span v-if="dataPoints > 0">数据点: {{ dataPoints }}</span>
        <span v-if="bufferUsage > 0">缓冲区: {{ (bufferUsage * 100).toFixed(1) }}%</span>
      </div>
    </div>
    
    <div class="canvas-container" ref="canvasContainer">
      <canvas 
        ref="canvas" 
        @resize="onCanvasResize"
        :width="canvasWidth"
        :height="canvasHeight"
      ></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { WaveformCalculator, type WaveformCalcConfig } from '@/utils/waveform_calc'
import { WaveformRenderer, type WaveformRenderConfig } from '@/utils/waveform'
import { PerformanceMonitor, MemoryOptimizer, RenderOptimizer } from '@/utils/performance'

// 响应式数据
const canvas = ref<HTMLCanvasElement>()
const canvasContainer = ref<HTMLDivElement>()
const canvasWidth = ref(1200)
const canvasHeight = ref(600)

const dataSource = ref<'file' | 'sse'>('file')
const sseUrl = ref('http://localhost:8081/sse')
const isLoading = ref(false)
const isConnected = ref(false)
const status = ref('就绪')
const dataPoints = ref(0)
const bufferUsage = ref(0)

const speedMultiplier = ref(1)
const voltageMultiplier = ref(1)

// 核心组件
let waveformCalculator: WaveformCalculator | null = null
let waveformRenderer: WaveformRenderer | null = null
let animationFrameId: number | null = null
let lastDrawnIndex = -1

// SSE 连接
let eventSource: EventSource | null = null

// 性能监控
const performanceMonitor = new PerformanceMonitor()
const memoryOptimizer = MemoryOptimizer.getInstance()

// 初始化
onMounted(async () => {
  await nextTick()
  initializeComponents()
  startRenderLoop()

  // 启动性能优化
  memoryOptimizer.startPeriodicGC(30000) // 30秒检查一次

  // 监听窗口大小变化
  window.addEventListener('resize', onWindowResize)
  onWindowResize() // 初始化大小
})

onUnmounted(() => {
  cleanup()
  memoryOptimizer.stopPeriodicGC()
  window.removeEventListener('resize', onWindowResize)
})

/**
 * 初始化组件
 */
function initializeComponents() {
  if (!canvas.value) return
  
  // 初始化波形计算器
  const calcConfig: WaveformCalcConfig = {
    bufferSize: 50000, // 100秒的数据 (500Hz * 100s)
    sampleRate: 500,
    benchmark: 2048,
    amplitude: 800
  }
  waveformCalculator = new WaveformCalculator(calcConfig)
  
  // 初始化波形渲染器
  const renderConfig = WaveformRenderer.getDefaultConfig()
  renderConfig.speedMultiplier = speedMultiplier.value
  renderConfig.voltageMultiplier = voltageMultiplier.value
  waveformRenderer = new WaveformRenderer(canvas.value, renderConfig)
  
  status.value = '已初始化'
}

/**
 * 开始渲染循环
 */
function startRenderLoop() {
  function render() {
    const endRenderTiming = performanceMonitor.startRenderTiming()

    if (waveformCalculator && waveformRenderer) {
      // 获取显示窗口的数据长度（10秒窗口）
      const displayLength = WaveformCalculator.calculateDisplayLength(10, 500)
      const waveformData = waveformCalculator.getWaveformData(displayLength)

      if (waveformData.length > 0) {
        lastDrawnIndex = waveformRenderer.drawWaveformRealtime(waveformData, lastDrawnIndex)

        // 更新状态
        dataPoints.value = waveformCalculator.getAvailableDataLength()
        bufferUsage.value = waveformCalculator.getBufferUsage()
      }
    }

    endRenderTiming()
    performanceMonitor.updateFPS()

    animationFrameId = requestAnimationFrame(render)
  }

  render()
}

/**
 * 窗口大小变化处理
 */
function onWindowResize() {
  if (!canvasContainer.value) return
  
  const rect = canvasContainer.value.getBoundingClientRect()
  canvasWidth.value = rect.width
  canvasHeight.value = rect.height
  
  // 强制重绘
  lastDrawnIndex = -1
}

/**
 * Canvas大小变化处理
 */
function onCanvasResize() {
  // 强制重绘网格
  lastDrawnIndex = -1
}

/**
 * 数据源变化处理
 */
function onDataSourceChange() {
  if (dataSource.value === 'sse' && isConnected.value) {
    disconnectSSE()
  }
  status.value = `切换到${dataSource.value === 'file' ? 'CSV文件' : 'SSE推送'}模式`
}

/**
 * 加载CSV文件
 */
async function loadCsvFile() {
  if (!window.electronAPI || !waveformCalculator) return
  
  isLoading.value = true
  status.value = '加载文件中...'
  
  try {
    const dataPath = await window.electronAPI.getDataPath()
    const result = await window.electronAPI.readCsvFile(`${dataPath}/waveform_714.csv`)
    
    if (result.success && result.data) {
      // 解析CSV数据
      const lines = result.data.trim().split('\n')
      const values: number[] = []
      
      for (const line of lines) {
        const columns = line.split(',')
        const lastColumn = columns[columns.length - 1]
        if (lastColumn && !isNaN(Number(lastColumn))) {
          values.push(Number(lastColumn))
        }
      }
      
      // 清空之前的数据
      waveformCalculator.clear()
      lastDrawnIndex = -1
      
      // 批量添加数据
      waveformCalculator.addDataPoints(values)
      
      status.value = `已加载 ${values.length} 个数据点`
    } else {
      status.value = `加载失败: ${result.error}`
    }
  } catch (error) {
    status.value = `加载错误: ${error}`
  } finally {
    isLoading.value = false
  }
}

/**
 * 切换SSE连接
 */
function toggleSSE() {
  if (isConnected.value) {
    disconnectSSE()
  } else {
    connectSSE()
  }
}

/**
 * 连接SSE
 */
function connectSSE() {
  if (!sseUrl.value || !waveformCalculator) return
  
  isLoading.value = true
  status.value = '连接SSE中...'
  
  try {
    eventSource = new EventSource(sseUrl.value)
    
    eventSource.onopen = () => {
      isConnected.value = true
      isLoading.value = false
      status.value = 'SSE已连接'
    }
    
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        if (typeof data === 'number') {
          waveformCalculator!.addDataPoint(data)
        } else if (Array.isArray(data)) {
          waveformCalculator!.addDataPoints(data)
        }
      } catch (error) {
        console.error('解析SSE数据失败:', error)
      }
    }
    
    eventSource.onerror = () => {
      status.value = 'SSE连接错误'
      disconnectSSE()
    }
  } catch (error) {
    status.value = `SSE连接失败: ${error}`
    isLoading.value = false
  }
}

/**
 * 断开SSE连接
 */
function disconnectSSE() {
  if (eventSource) {
    eventSource.close()
    eventSource = null
  }
  isConnected.value = false
  status.value = 'SSE已断开'
}

/**
 * 更新渲染配置
 */
function updateRenderConfig() {
  if (waveformRenderer) {
    waveformRenderer.updateConfig({
      speedMultiplier: speedMultiplier.value,
      voltageMultiplier: voltageMultiplier.value
    })
    lastDrawnIndex = -1 // 强制重绘
  }
}

/**
 * 清空显示
 */
function clearDisplay() {
  if (waveformCalculator) {
    waveformCalculator.clear()
  }
  if (waveformRenderer) {
    waveformRenderer.clear()
  }
  lastDrawnIndex = -1
  dataPoints.value = 0
  bufferUsage.value = 0
  status.value = '显示已清空'
}

/**
 * 清理资源
 */
function cleanup() {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
  disconnectSSE()
}
</script>

<style scoped>
.ecg-display {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #1a1a1a;
  color: white;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 10px;
  background: #2a2a2a;
  border-bottom: 1px solid #444;
  align-items: center;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-group label {
  font-size: 12px;
  color: #ccc;
}

.control-group select,
.control-group input,
.control-group button {
  padding: 4px 8px;
  border: 1px solid #555;
  background: #333;
  color: white;
  border-radius: 3px;
  font-size: 12px;
}

.control-group button:hover:not(:disabled) {
  background: #444;
}

.control-group button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.status {
  margin-left: auto;
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #aaa;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

canvas {
  display: block;
  background: #000;
}
</style>
