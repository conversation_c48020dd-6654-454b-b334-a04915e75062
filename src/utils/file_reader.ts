/**
 * 文件读取模块
 * 读取csv，调用waveform_calc计算，绘制波形
 */

import type { WaveformCalculator } from './waveform_calc'

export interface FileReaderConfig {
  chunkSize: number // 每次处理的数据块大小
  delimiter: string // CSV分隔符
  targetColumn: number // 目标列索引（-1表示最后一列）
  skipEmptyLines: boolean // 是否跳过空行
  skipHeader: boolean // 是否跳过标题行
  outputFrequency: number // 输出频率 (Hz)，默认 500Hz
}

export interface ParseResult {
  success: boolean
  data?: number[]
  error?: string
  totalLines?: number
  validLines?: number
}

export class FileReader {
  private config: FileReaderConfig
  private waveformCalculator: WaveformCalculator | null = null
  private outputTimer: number | null = null
  private isOutputting: boolean = false

  constructor(config: FileReaderConfig) {
    this.config = config
  }

  /**
   * 设置波形计算器
   */
  setWaveformCalculator(calculator: WaveformCalculator): void {
    this.waveformCalculator = calculator
  }

  /**
   * 解析CSV文本数据
   */
  parseCSVText(csvText: string): ParseResult {
    try {
      const lines = csvText.split('\n')
      const values: number[] = []
      let totalLines = lines.length
      let validLines = 0
      let startIndex = this.config.skipHeader ? 1 : 0

      for (let i = startIndex; i < lines.length; i++) {
        const line = lines[i].trim()
        
        // 跳过空行
        if (this.config.skipEmptyLines && !line) {
          continue
        }

        const columns = line.split(this.config.delimiter)
        
        // 确定目标列
        const targetIndex = this.config.targetColumn === -1 
          ? columns.length - 1 
          : this.config.targetColumn

        if (targetIndex >= 0 && targetIndex < columns.length) {
          const value = columns[targetIndex].trim()
          
          if (value && !isNaN(Number(value))) {
            values.push(Number(value))
            validLines++
          }
        }
      }

      return {
        success: true,
        data: values,
        totalLines,
        validLines
      }
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      }
    }
  }

  /**
   * 分块处理大文件
   */
  parseCSVTextChunked(csvText: string, onProgress?: (progress: number) => void): Promise<ParseResult> {
    return new Promise((resolve) => {
      const lines = csvText.split('\n')
      const values: number[] = []
      let totalLines = lines.length
      let validLines = 0
      let processedLines = 0
      let startIndex = this.config.skipHeader ? 1 : 0

      const processChunk = () => {
        const endIndex = Math.min(startIndex + this.config.chunkSize, lines.length)
        
        for (let i = startIndex; i < endIndex; i++) {
          const line = lines[i].trim()
          
          // 跳过空行
          if (this.config.skipEmptyLines && !line) {
            continue
          }

          const columns = line.split(this.config.delimiter)
          
          // 确定目标列
          const targetIndex = this.config.targetColumn === -1 
            ? columns.length - 1 
            : this.config.targetColumn

          if (targetIndex >= 0 && targetIndex < columns.length) {
            const value = columns[targetIndex].trim()
            
            if (value && !isNaN(Number(value))) {
              values.push(Number(value))
              validLines++
            }
          }
        }

        processedLines = endIndex
        
        // 报告进度
        if (onProgress) {
          const progress = processedLines / totalLines
          onProgress(progress)
        }

        // 如果还有数据要处理，继续下一块
        if (endIndex < lines.length) {
          startIndex = endIndex
          // 使用 setTimeout 避免阻塞 UI
          setTimeout(processChunk, 0)
        } else {
          // 处理完成
          resolve({
            success: true,
            data: values,
            totalLines,
            validLines
          })
        }
      }

      // 开始处理
      processChunk()
    })
  }

  /**
   * 读取并解析CSV文件（通过Electron API）
   */
  async readAndParseCSVFile(
    filePath: string, 
    onProgress?: (progress: number) => void
  ): Promise<ParseResult> {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API not available')
      }

      const result = await window.electronAPI.readCsvFile(filePath)
      
      if (!result.success || !result.data) {
        return {
          success: false,
          error: result.error || 'Failed to read file'
        }
      }

      // 使用分块处理大文件
      return await this.parseCSVTextChunked(result.data, onProgress)
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      }
    }
  }

  /**
   * 读取文件并直接添加到波形计算器
   */
  async loadFileToCalculator(
    filePath: string,
    onProgress?: (progress: number, stage: string) => void
  ): Promise<boolean> {
    if (!this.waveformCalculator) {
      console.error('WaveformCalculator not set')
      return false
    }

    try {
      // 报告读取阶段
      if (onProgress) {
        onProgress(0, '读取文件中...')
      }

      const parseResult = await this.readAndParseCSVFile(filePath, (progress) => {
        if (onProgress) {
          onProgress(progress * 0.8, '解析数据中...')
        }
      })

      if (!parseResult.success || !parseResult.data) {
        console.error('Failed to parse CSV:', parseResult.error)
        return false
      }

      // 报告添加数据阶段
      if (onProgress) {
        onProgress(0.8, '添加数据到缓冲区...')
      }

      // 清空之前的数据
      this.waveformCalculator.clear()

      // 分批添加数据以避免阻塞
      const data = parseResult.data
      const batchSize = 1000
      let addedCount = 0

      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize)
        this.waveformCalculator.addDataPoints(batch)
        addedCount += batch.length

        // 报告进度
        if (onProgress) {
          const progress = 0.8 + (addedCount / data.length) * 0.2
          onProgress(progress, `已添加 ${addedCount}/${data.length} 个数据点`)
        }

        // 让出控制权给UI
        await new Promise(resolve => setTimeout(resolve, 0))
      }

      if (onProgress) {
        onProgress(1, `完成！加载了 ${data.length} 个数据点`)
      }

      return true
    } catch (error) {
      console.error('Error loading file to calculator:', error)
      return false
    }
  }

  /**
   * 按照指定频率输出数据到波形计算器
   * @param data 要输出的数据数组
   * @param onProgress 进度回调函数
   * @param onComplete 完成回调函数
   * @returns Promise<boolean> 是否成功启动输出
   */
  async startFrequencyOutput(
    data: number[],
    onProgress?: (current: number, total: number, value: number) => void,
    onComplete?: () => void
  ): Promise<boolean> {
    if (!this.waveformCalculator) {
      console.error('WaveformCalculator not set')
      return false
    }

    if (this.isOutputting) {
      console.warn('Already outputting data')
      return false
    }

    if (data.length === 0) {
      console.warn('No data to output')
      return false
    }

    this.isOutputting = true
    const intervalMs = 1000 / this.config.outputFrequency // 计算间隔时间
    let currentIndex = 0
    let startTime = performance.now()
    let nextScheduledTime = startTime

    console.log(`开始按 ${this.config.outputFrequency}Hz 频率输出数据，间隔 ${intervalMs}ms`)

    return new Promise((resolve) => {
      const outputNextValue = () => {
        if (!this.isOutputting || currentIndex >= data.length) {
          this.stopFrequencyOutput()
          if (onComplete) {
            onComplete()
          }
          resolve(true)
          return
        }

        const value = data[currentIndex]

        // 添加数据到波形计算器
        if (this.waveformCalculator) {
          this.waveformCalculator.addDataPoint(value)
        }

        // 调用进度回调
        if (onProgress) {
          onProgress(currentIndex + 1, data.length, value)
        }

        currentIndex++
        nextScheduledTime += intervalMs

        // 计算下一次输出的精确延迟
        const currentTime = performance.now()
        const delay = Math.max(0, nextScheduledTime - currentTime)

        // 设置下一次输出
        this.outputTimer = window.setTimeout(outputNextValue, delay)
      }

      // 开始输出
      outputNextValue()
      resolve(true)
    })
  }

  /**
   * 停止频率输出
   */
  stopFrequencyOutput(): void {
    this.isOutputting = false
    if (this.outputTimer) {
      clearTimeout(this.outputTimer)
      this.outputTimer = null
    }
    console.log('频率输出已停止')
  }

  /**
   * 检查是否正在输出
   */
  isFrequencyOutputting(): boolean {
    return this.isOutputting
  }

  /**
   * 读取文件并按频率输出到波形计算器
   * @param filePath 文件路径
   * @param onProgress 进度回调
   * @param onDataProgress 数据输出进度回调
   * @param onComplete 完成回调
   */
  async loadFileAndStartFrequencyOutput(
    filePath: string,
    onProgress?: (progress: number, stage: string) => void,
    onDataProgress?: (current: number, total: number, value: number) => void,
    onComplete?: () => void
  ): Promise<boolean> {
    try {
      // 报告读取阶段
      if (onProgress) {
        onProgress(0, '读取文件中...')
      }

      const parseResult = await this.readAndParseCSVFile(filePath, (progress) => {
        if (onProgress) {
          onProgress(progress * 0.5, '解析数据中...')
        }
      })

      if (!parseResult.success || !parseResult.data) {
        console.error('Failed to parse CSV:', parseResult.error)
        return false
      }

      if (onProgress) {
        onProgress(0.5, '准备按频率输出数据...')
      }

      // 清空之前的数据
      if (this.waveformCalculator) {
        this.waveformCalculator.clear()
      }

      if (onProgress) {
        onProgress(0.6, `开始按 ${this.config.outputFrequency}Hz 频率输出...`)
      }

      // 开始按频率输出数据
      const success = await this.startFrequencyOutput(
        parseResult.data,
        (current, total, value) => {
          // 数据输出进度
          if (onDataProgress) {
            onDataProgress(current, total, value)
          }

          // 总体进度
          if (onProgress) {
            const dataProgress = current / total
            const overallProgress = 0.6 + (dataProgress * 0.4)
            onProgress(overallProgress, `输出数据: ${current}/${total} (${value})`)
          }
        },
        () => {
          if (onProgress) {
            onProgress(1, `完成！按 ${this.config.outputFrequency}Hz 输出了 ${parseResult.data!.length} 个数据点`)
          }
          if (onComplete) {
            onComplete()
          }
        }
      )

      return success
    } catch (error) {
      console.error('Error in loadFileAndStartFrequencyOutput:', error)
      return false
    }
  }

  /**
   * 获取默认配置
   */
  static getDefaultConfig(): FileReaderConfig {
    return {
      chunkSize: 1000,
      delimiter: ',',
      targetColumn: -1, // 最后一列
      skipEmptyLines: true,
      skipHeader: false,
      outputFrequency: 500 // 500Hz 输出频率
    }
  }

  /**
   * 验证CSV格式
   */
  static validateCSVFormat(csvText: string, config: FileReaderConfig): {
    valid: boolean
    error?: string
    sampleData?: number[]
  } {
    try {
      const lines = csvText.split('\n').slice(0, 10) // 只检查前10行
      const sampleData: number[] = []
      let validRows = 0

      for (const line of lines) {
        if (!line.trim()) continue

        const columns = line.split(config.delimiter)
        const targetIndex = config.targetColumn === -1 
          ? columns.length - 1 
          : config.targetColumn

        if (targetIndex >= 0 && targetIndex < columns.length) {
          const value = columns[targetIndex].trim()
          
          if (value && !isNaN(Number(value))) {
            sampleData.push(Number(value))
            validRows++
          }
        }
      }

      if (validRows === 0) {
        return {
          valid: false,
          error: '未找到有效的数值数据'
        }
      }

      return {
        valid: true,
        sampleData
      }
    } catch (error) {
      return {
        valid: false,
        error: (error as Error).message
      }
    }
  }
}
