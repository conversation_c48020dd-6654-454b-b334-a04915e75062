var Qn=Object.defineProperty;var er=(e,t,s)=>t in e?Qn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s;var G=(e,t,s)=>er(e,typeof t!="symbol"?t+"":t,s);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))i(n);new MutationObserver(n=>{for(const r of n)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&i(o)}).observe(document,{childList:!0,subtree:!0});function s(n){const r={};return n.integrity&&(r.integrity=n.integrity),n.referrerPolicy&&(r.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?r.credentials="include":n.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function i(n){if(n.ep)return;n.ep=!0;const r=s(n);fetch(n.href,r)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ws(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const K={},vt=[],Fe=()=>{},tr=()=>!1,fs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Gs=e=>e.startsWith("onUpdate:"),ie=Object.assign,Bs=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},sr=Object.prototype.hasOwnProperty,j=(e,t)=>sr.call(e,t),F=Array.isArray,_t=e=>Kt(e)==="[object Map]",St=e=>Kt(e)==="[object Set]",pi=e=>Kt(e)==="[object Date]",M=e=>typeof e=="function",ee=e=>typeof e=="string",Re=e=>typeof e=="symbol",J=e=>e!==null&&typeof e=="object",ki=e=>(J(e)||M(e))&&M(e.then)&&M(e.catch),Yi=Object.prototype.toString,Kt=e=>Yi.call(e),ir=e=>Kt(e).slice(8,-1),Ji=e=>Kt(e)==="[object Object]",Ks=e=>ee(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Rt=Ws(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),us=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},nr=/-(\w)/g,Xe=us(e=>e.replace(nr,(t,s)=>s?s.toUpperCase():"")),rr=/\B([A-Z])/g,ct=us(e=>e.replace(rr,"-$1").toLowerCase()),Xi=us(e=>e.charAt(0).toUpperCase()+e.slice(1)),vs=us(e=>e?`on${Xi(e)}`:""),Je=(e,t)=>!Object.is(e,t),Qt=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},Fs=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},is=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let gi;const as=()=>gi||(gi=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function zs(e){if(F(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],n=ee(i)?fr(i):zs(i);if(n)for(const r in n)t[r]=n[r]}return t}else if(ee(e)||J(e))return e}const or=/;(?![^(]*\))/g,lr=/:([^]+)/,cr=/\/\*[^]*?\*\//g;function fr(e){const t={};return e.replace(cr,"").split(or).forEach(s=>{if(s){const i=s.split(lr);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function qs(e){let t="";if(ee(e))t=e;else if(F(e))for(let s=0;s<e.length;s++){const i=qs(e[s]);i&&(t+=i+" ")}else if(J(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const ur="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ar=Ws(ur);function Zi(e){return!!e||e===""}function dr(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=zt(e[i],t[i]);return s}function zt(e,t){if(e===t)return!0;let s=pi(e),i=pi(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=Re(e),i=Re(t),s||i)return e===t;if(s=F(e),i=F(t),s||i)return s&&i?dr(e,t):!1;if(s=J(e),i=J(t),s||i){if(!s||!i)return!1;const n=Object.keys(e).length,r=Object.keys(t).length;if(n!==r)return!1;for(const o in e){const l=e.hasOwnProperty(o),c=t.hasOwnProperty(o);if(l&&!c||!l&&c||!zt(e[o],t[o]))return!1}}return String(e)===String(t)}function ks(e,t){return e.findIndex(s=>zt(s,t))}const Qi=e=>!!(e&&e.__v_isRef===!0),gt=e=>ee(e)?e:e==null?"":F(e)||J(e)&&(e.toString===Yi||!M(e.toString))?Qi(e)?gt(e.value):JSON.stringify(e,en,2):String(e),en=(e,t)=>Qi(t)?en(e,t.value):_t(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,n],r)=>(s[_s(i,r)+" =>"]=n,s),{})}:St(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>_s(s))}:Re(t)?_s(t):J(t)&&!F(t)&&!Ji(t)?String(t):t,_s=(e,t="")=>{var s;return Re(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ae;class hr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ae,!t&&ae&&(this.index=(ae.scopes||(ae.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=ae;try{return ae=this,t()}finally{ae=s}}}on(){++this._on===1&&(this.prevScope=ae,ae=this)}off(){this._on>0&&--this._on===0&&(ae=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function pr(){return ae}let Y;const bs=new WeakSet;class tn{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ae&&ae.active&&ae.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,bs.has(this)&&(bs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||nn(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,mi(this),rn(this);const t=Y,s=xe;Y=this,xe=!0;try{return this.fn()}finally{on(this),Y=t,xe=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Xs(t);this.deps=this.depsTail=void 0,mi(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?bs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){As(this)&&this.run()}get dirty(){return As(this)}}let sn=0,It,Mt;function nn(e,t=!1){if(e.flags|=8,t){e.next=Mt,Mt=e;return}e.next=It,It=e}function Ys(){sn++}function Js(){if(--sn>0)return;if(Mt){let t=Mt;for(Mt=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;It;){let t=It;for(It=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function rn(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function on(e){let t,s=e.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),Xs(i),gr(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}e.deps=t,e.depsTail=s}function As(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ln(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ln(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Nt)||(e.globalVersion=Nt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!As(e))))return;e.flags|=2;const t=e.dep,s=Y,i=xe;Y=e,xe=!0;try{rn(e);const n=e.fn(e._value);(t.version===0||Je(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{Y=s,xe=i,on(e),e.flags&=-3}}function Xs(e,t=!1){const{dep:s,prevSub:i,nextSub:n}=e;if(i&&(i.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=i,e.nextSub=void 0),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let r=s.computed.deps;r;r=r.nextDep)Xs(r,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function gr(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let xe=!0;const cn=[];function Ge(){cn.push(xe),xe=!1}function Be(){const e=cn.pop();xe=e===void 0?!0:e}function mi(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=Y;Y=void 0;try{t()}finally{Y=s}}}let Nt=0;class mr{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Zs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!Y||!xe||Y===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==Y)s=this.activeLink=new mr(Y,this),Y.deps?(s.prevDep=Y.depsTail,Y.depsTail.nextDep=s,Y.depsTail=s):Y.deps=Y.depsTail=s,fn(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=Y.depsTail,s.nextDep=void 0,Y.depsTail.nextDep=s,Y.depsTail=s,Y.deps===s&&(Y.deps=i)}return s}trigger(t){this.version++,Nt++,this.notify(t)}notify(t){Ys();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Js()}}}function fn(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)fn(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Rs=new WeakMap,ot=Symbol(""),Is=Symbol(""),jt=Symbol("");function te(e,t,s){if(xe&&Y){let i=Rs.get(e);i||Rs.set(e,i=new Map);let n=i.get(s);n||(i.set(s,n=new Zs),n.map=i,n.key=s),n.track()}}function je(e,t,s,i,n,r){const o=Rs.get(e);if(!o){Nt++;return}const l=c=>{c&&c.trigger()};if(Ys(),t==="clear")o.forEach(l);else{const c=F(e),d=c&&Ks(s);if(c&&s==="length"){const a=Number(i);o.forEach((p,x)=>{(x==="length"||x===jt||!Re(x)&&x>=a)&&l(p)})}else switch((s!==void 0||o.has(void 0))&&l(o.get(s)),d&&l(o.get(jt)),t){case"add":c?d&&l(o.get("length")):(l(o.get(ot)),_t(e)&&l(o.get(Is)));break;case"delete":c||(l(o.get(ot)),_t(e)&&l(o.get(Is)));break;case"set":_t(e)&&l(o.get(ot));break}}Js()}function ht(e){const t=N(e);return t===e?t:(te(t,"iterate",jt),ye(e)?t:t.map(oe))}function Qs(e){return te(e=N(e),"iterate",jt),e}const vr={__proto__:null,[Symbol.iterator](){return xs(this,Symbol.iterator,oe)},concat(...e){return ht(this).concat(...e.map(t=>F(t)?ht(t):t))},entries(){return xs(this,"entries",e=>(e[1]=oe(e[1]),e))},every(e,t){return $e(this,"every",e,t,void 0,arguments)},filter(e,t){return $e(this,"filter",e,t,s=>s.map(oe),arguments)},find(e,t){return $e(this,"find",e,t,oe,arguments)},findIndex(e,t){return $e(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return $e(this,"findLast",e,t,oe,arguments)},findLastIndex(e,t){return $e(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return $e(this,"forEach",e,t,void 0,arguments)},includes(...e){return ys(this,"includes",e)},indexOf(...e){return ys(this,"indexOf",e)},join(e){return ht(this).join(e)},lastIndexOf(...e){return ys(this,"lastIndexOf",e)},map(e,t){return $e(this,"map",e,t,void 0,arguments)},pop(){return Et(this,"pop")},push(...e){return Et(this,"push",e)},reduce(e,...t){return vi(this,"reduce",e,t)},reduceRight(e,...t){return vi(this,"reduceRight",e,t)},shift(){return Et(this,"shift")},some(e,t){return $e(this,"some",e,t,void 0,arguments)},splice(...e){return Et(this,"splice",e)},toReversed(){return ht(this).toReversed()},toSorted(e){return ht(this).toSorted(e)},toSpliced(...e){return ht(this).toSpliced(...e)},unshift(...e){return Et(this,"unshift",e)},values(){return xs(this,"values",oe)}};function xs(e,t,s){const i=Qs(e),n=i[t]();return i!==e&&!ye(e)&&(n._next=n.next,n.next=()=>{const r=n._next();return r.value&&(r.value=s(r.value)),r}),n}const _r=Array.prototype;function $e(e,t,s,i,n,r){const o=Qs(e),l=o!==e&&!ye(e),c=o[t];if(c!==_r[t]){const p=c.apply(e,r);return l?oe(p):p}let d=s;o!==e&&(l?d=function(p,x){return s.call(this,oe(p),x,e)}:s.length>2&&(d=function(p,x){return s.call(this,p,x,e)}));const a=c.call(o,d,i);return l&&n?n(a):a}function vi(e,t,s,i){const n=Qs(e);let r=s;return n!==e&&(ye(e)?s.length>3&&(r=function(o,l,c){return s.call(this,o,l,c,e)}):r=function(o,l,c){return s.call(this,o,oe(l),c,e)}),n[t](r,...i)}function ys(e,t,s){const i=N(e);te(i,"iterate",jt);const n=i[t](...s);return(n===-1||n===!1)&&ii(s[0])?(s[0]=N(s[0]),i[t](...s)):n}function Et(e,t,s=[]){Ge(),Ys();const i=N(e)[t].apply(e,s);return Js(),Be(),i}const br=Ws("__proto__,__v_isRef,__isVue"),un=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Re));function xr(e){Re(e)||(e=String(e));const t=N(this);return te(t,"has",e),t.hasOwnProperty(e)}class an{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,r=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return r;if(s==="__v_raw")return i===(n?r?Ar:gn:r?pn:hn).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const o=F(t);if(!n){let c;if(o&&(c=vr[s]))return c;if(s==="hasOwnProperty")return xr}const l=Reflect.get(t,s,se(t)?t:i);return(Re(s)?un.has(s):br(s))||(n||te(t,"get",s),r)?l:se(l)?o&&Ks(s)?l:l.value:J(l)?n?mn(l):ti(l):l}}class dn extends an{constructor(t=!1){super(!1,t)}set(t,s,i,n){let r=t[s];if(!this._isShallow){const c=lt(r);if(!ye(i)&&!lt(i)&&(r=N(r),i=N(i)),!F(t)&&se(r)&&!se(i))return c?!1:(r.value=i,!0)}const o=F(t)&&Ks(s)?Number(s)<t.length:j(t,s),l=Reflect.set(t,s,i,se(t)?t:n);return t===N(n)&&(o?Je(i,r)&&je(t,"set",s,i):je(t,"add",s,i)),l}deleteProperty(t,s){const i=j(t,s);t[s];const n=Reflect.deleteProperty(t,s);return n&&i&&je(t,"delete",s,void 0),n}has(t,s){const i=Reflect.has(t,s);return(!Re(s)||!un.has(s))&&te(t,"has",s),i}ownKeys(t){return te(t,"iterate",F(t)?"length":ot),Reflect.ownKeys(t)}}class yr extends an{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const wr=new dn,Sr=new yr,Cr=new dn(!0);const Ms=e=>e,Jt=e=>Reflect.getPrototypeOf(e);function Tr(e,t,s){return function(...i){const n=this.__v_raw,r=N(n),o=_t(r),l=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,d=n[e](...i),a=s?Ms:t?Ds:oe;return!t&&te(r,"iterate",c?Is:ot),{next(){const{value:p,done:x}=d.next();return x?{value:p,done:x}:{value:l?[a(p[0]),a(p[1])]:a(p),done:x}},[Symbol.iterator](){return this}}}}function Xt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Pr(e,t){const s={get(n){const r=this.__v_raw,o=N(r),l=N(n);e||(Je(n,l)&&te(o,"get",n),te(o,"get",l));const{has:c}=Jt(o),d=t?Ms:e?Ds:oe;if(c.call(o,n))return d(r.get(n));if(c.call(o,l))return d(r.get(l));r!==o&&r.get(n)},get size(){const n=this.__v_raw;return!e&&te(N(n),"iterate",ot),Reflect.get(n,"size",n)},has(n){const r=this.__v_raw,o=N(r),l=N(n);return e||(Je(n,l)&&te(o,"has",n),te(o,"has",l)),n===l?r.has(n):r.has(n)||r.has(l)},forEach(n,r){const o=this,l=o.__v_raw,c=N(l),d=t?Ms:e?Ds:oe;return!e&&te(c,"iterate",ot),l.forEach((a,p)=>n.call(r,d(a),d(p),o))}};return ie(s,e?{add:Xt("add"),set:Xt("set"),delete:Xt("delete"),clear:Xt("clear")}:{add(n){!t&&!ye(n)&&!lt(n)&&(n=N(n));const r=N(this);return Jt(r).has.call(r,n)||(r.add(n),je(r,"add",n,n)),this},set(n,r){!t&&!ye(r)&&!lt(r)&&(r=N(r));const o=N(this),{has:l,get:c}=Jt(o);let d=l.call(o,n);d||(n=N(n),d=l.call(o,n));const a=c.call(o,n);return o.set(n,r),d?Je(r,a)&&je(o,"set",n,r):je(o,"add",n,r),this},delete(n){const r=N(this),{has:o,get:l}=Jt(r);let c=o.call(r,n);c||(n=N(n),c=o.call(r,n)),l&&l.call(r,n);const d=r.delete(n);return c&&je(r,"delete",n,void 0),d},clear(){const n=N(this),r=n.size!==0,o=n.clear();return r&&je(n,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=Tr(n,e,t)}),s}function ei(e,t){const s=Pr(e,t);return(i,n,r)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?i:Reflect.get(j(s,n)&&n in i?s:i,n,r)}const Er={get:ei(!1,!1)},Or={get:ei(!1,!0)},Fr={get:ei(!0,!1)};const hn=new WeakMap,pn=new WeakMap,gn=new WeakMap,Ar=new WeakMap;function Rr(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ir(e){return e.__v_skip||!Object.isExtensible(e)?0:Rr(ir(e))}function ti(e){return lt(e)?e:si(e,!1,wr,Er,hn)}function Mr(e){return si(e,!1,Cr,Or,pn)}function mn(e){return si(e,!0,Sr,Fr,gn)}function si(e,t,s,i,n){if(!J(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=Ir(e);if(r===0)return e;const o=n.get(e);if(o)return o;const l=new Proxy(e,r===2?i:s);return n.set(e,l),l}function Dt(e){return lt(e)?Dt(e.__v_raw):!!(e&&e.__v_isReactive)}function lt(e){return!!(e&&e.__v_isReadonly)}function ye(e){return!!(e&&e.__v_isShallow)}function ii(e){return e?!!e.__v_raw:!1}function N(e){const t=e&&e.__v_raw;return t?N(t):e}function Dr(e){return!j(e,"__v_skip")&&Object.isExtensible(e)&&Fs(e,"__v_skip",!0),e}const oe=e=>J(e)?ti(e):e,Ds=e=>J(e)?mn(e):e;function se(e){return e?e.__v_isRef===!0:!1}function ne(e){return Vr(e,!1)}function Vr(e,t){return se(e)?e:new $r(e,t)}class $r{constructor(t,s){this.dep=new Zs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:N(t),this._value=s?t:oe(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||ye(t)||lt(t);t=i?t:N(t),Je(t,s)&&(this._rawValue=t,this._value=i?t:oe(t),this.dep.trigger())}}function Lr(e){return se(e)?e.value:e}const Hr={get:(e,t,s)=>t==="__v_raw"?e:Lr(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const n=e[t];return se(n)&&!se(s)?(n.value=s,!0):Reflect.set(e,t,s,i)}};function vn(e){return Dt(e)?e:new Proxy(e,Hr)}class Nr{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Zs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Nt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&Y!==this)return nn(this,!0),!0}get value(){const t=this.dep.track();return ln(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function jr(e,t,s=!1){let i,n;return M(e)?i=e:(i=e.get,n=e.set),new Nr(i,n,s)}const Zt={},ns=new WeakMap;let nt;function Ur(e,t=!1,s=nt){if(s){let i=ns.get(s);i||ns.set(s,i=[]),i.push(e)}}function Wr(e,t,s=K){const{immediate:i,deep:n,once:r,scheduler:o,augmentJob:l,call:c}=s,d=P=>n?P:ye(P)||n===!1||n===0?Ue(P,1):Ue(P);let a,p,x,C,I=!1,A=!1;if(se(e)?(p=()=>e.value,I=ye(e)):Dt(e)?(p=()=>d(e),I=!0):F(e)?(A=!0,I=e.some(P=>Dt(P)||ye(P)),p=()=>e.map(P=>{if(se(P))return P.value;if(Dt(P))return d(P);if(M(P))return c?c(P,2):P()})):M(e)?t?p=c?()=>c(e,2):e:p=()=>{if(x){Ge();try{x()}finally{Be()}}const P=nt;nt=a;try{return c?c(e,3,[C]):e(C)}finally{nt=P}}:p=Fe,t&&n){const P=p,B=n===!0?1/0:n;p=()=>Ue(P(),B)}const D=pr(),V=()=>{a.stop(),D&&D.active&&Bs(D.effects,a)};if(r&&t){const P=t;t=(...B)=>{P(...B),V()}}let U=A?new Array(e.length).fill(Zt):Zt;const z=P=>{if(!(!(a.flags&1)||!a.dirty&&!P))if(t){const B=a.run();if(n||I||(A?B.some((_e,de)=>Je(_e,U[de])):Je(B,U))){x&&x();const _e=nt;nt=a;try{const de=[B,U===Zt?void 0:A&&U[0]===Zt?[]:U,C];U=B,c?c(t,3,de):t(...de)}finally{nt=_e}}}else a.run()};return l&&l(z),a=new tn(p),a.scheduler=o?()=>o(z,!1):z,C=P=>Ur(P,!1,a),x=a.onStop=()=>{const P=ns.get(a);if(P){if(c)c(P,4);else for(const B of P)B();ns.delete(a)}},t?i?z(!0):U=a.run():o?o(z.bind(null,!0),!0):a.run(),V.pause=a.pause.bind(a),V.resume=a.resume.bind(a),V.stop=V,V}function Ue(e,t=1/0,s){if(t<=0||!J(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,se(e))Ue(e.value,t,s);else if(F(e))for(let i=0;i<e.length;i++)Ue(e[i],t,s);else if(St(e)||_t(e))e.forEach(i=>{Ue(i,t,s)});else if(Ji(e)){for(const i in e)Ue(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&Ue(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function qt(e,t,s,i){try{return i?e(...i):e()}catch(n){ds(n,t,s)}}function Ie(e,t,s,i){if(M(e)){const n=qt(e,t,s,i);return n&&ki(n)&&n.catch(r=>{ds(r,t,s)}),n}if(F(e)){const n=[];for(let r=0;r<e.length;r++)n.push(Ie(e[r],t,s,i));return n}}function ds(e,t,s,i=!0){const n=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||K;if(t){let l=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const a=l.ec;if(a){for(let p=0;p<a.length;p++)if(a[p](e,c,d)===!1)return}l=l.parent}if(r){Ge(),qt(r,null,10,[e,c,d]),Be();return}}Gr(e,s,n,i,o)}function Gr(e,t,s,i=!0,n=!1){if(n)throw e;console.error(e)}const le=[];let Ee=-1;const bt=[];let qe=null,mt=0;const _n=Promise.resolve();let rs=null;function ni(e){const t=rs||_n;return e?t.then(this?e.bind(this):e):t}function Br(e){let t=Ee+1,s=le.length;for(;t<s;){const i=t+s>>>1,n=le[i],r=Ut(n);r<e||r===e&&n.flags&2?t=i+1:s=i}return t}function ri(e){if(!(e.flags&1)){const t=Ut(e),s=le[le.length-1];!s||!(e.flags&2)&&t>=Ut(s)?le.push(e):le.splice(Br(t),0,e),e.flags|=1,bn()}}function bn(){rs||(rs=_n.then(yn))}function Kr(e){F(e)?bt.push(...e):qe&&e.id===-1?qe.splice(mt+1,0,e):e.flags&1||(bt.push(e),e.flags|=1),bn()}function _i(e,t,s=Ee+1){for(;s<le.length;s++){const i=le[s];if(i&&i.flags&2){if(e&&i.id!==e.uid)continue;le.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function xn(e){if(bt.length){const t=[...new Set(bt)].sort((s,i)=>Ut(s)-Ut(i));if(bt.length=0,qe){qe.push(...t);return}for(qe=t,mt=0;mt<qe.length;mt++){const s=qe[mt];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}qe=null,mt=0}}const Ut=e=>e.id==null?e.flags&2?-1:1/0:e.id;function yn(e){try{for(Ee=0;Ee<le.length;Ee++){const t=le[Ee];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),qt(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ee<le.length;Ee++){const t=le[Ee];t&&(t.flags&=-2)}Ee=-1,le.length=0,xn(),rs=null,(le.length||bt.length)&&yn()}}let ve=null,wn=null;function os(e){const t=ve;return ve=e,wn=e&&e.type.__scopeId||null,t}function zr(e,t=ve,s){if(!t||e._n)return e;const i=(...n)=>{i._d&&Ei(-1);const r=os(t);let o;try{o=e(...n)}finally{os(r),i._d&&Ei(1)}return o};return i._n=!0,i._c=!0,i._d=!0,i}function Ot(e,t){if(ve===null)return e;const s=ms(ve),i=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[r,o,l,c=K]=t[n];r&&(M(r)&&(r={mounted:r,updated:r}),r.deep&&Ue(o),i.push({dir:r,instance:s,value:o,oldValue:void 0,arg:l,modifiers:c}))}return e}function st(e,t,s,i){const n=e.dirs,r=t&&t.dirs;for(let o=0;o<n.length;o++){const l=n[o];r&&(l.oldValue=r[o].value);let c=l.dir[i];c&&(Ge(),Ie(c,s,8,[e.el,l,e,t]),Be())}}const qr=Symbol("_vte"),kr=e=>e.__isTeleport;function oi(e,t){e.shapeFlag&6&&e.component?(e.transition=t,oi(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Sn(e,t){return M(e)?ie({name:e.name},t,{setup:e}):e}function Cn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Vt(e,t,s,i,n=!1){if(F(e)){e.forEach((I,A)=>Vt(I,t&&(F(t)?t[A]:t),s,i,n));return}if($t(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&Vt(e,t,s,i.component.subTree);return}const r=i.shapeFlag&4?ms(i.component):i.el,o=n?null:r,{i:l,r:c}=e,d=t&&t.r,a=l.refs===K?l.refs={}:l.refs,p=l.setupState,x=N(p),C=p===K?()=>!1:I=>j(x,I);if(d!=null&&d!==c&&(ee(d)?(a[d]=null,C(d)&&(p[d]=null)):se(d)&&(d.value=null)),M(c))qt(c,l,12,[o,a]);else{const I=ee(c),A=se(c);if(I||A){const D=()=>{if(e.f){const V=I?C(c)?p[c]:a[c]:c.value;n?F(V)&&Bs(V,r):F(V)?V.includes(r)||V.push(r):I?(a[c]=[r],C(c)&&(p[c]=a[c])):(c.value=[r],e.k&&(a[e.k]=c.value))}else I?(a[c]=o,C(c)&&(p[c]=o)):A&&(c.value=o,e.k&&(a[e.k]=o))};o?(D.id=-1,pe(D,s)):D()}}}as().requestIdleCallback;as().cancelIdleCallback;const $t=e=>!!e.type.__asyncLoader,Tn=e=>e.type.__isKeepAlive;function Yr(e,t){Pn(e,"a",t)}function Jr(e,t){Pn(e,"da",t)}function Pn(e,t,s=ce){const i=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(hs(t,i,s),s){let n=s.parent;for(;n&&n.parent;)Tn(n.parent.vnode)&&Xr(i,t,s,n),n=n.parent}}function Xr(e,t,s,i){const n=hs(t,e,i,!0);li(()=>{Bs(i[t],n)},s)}function hs(e,t,s=ce,i=!1){if(s){const n=s[e]||(s[e]=[]),r=t.__weh||(t.__weh=(...o)=>{Ge();const l=kt(s),c=Ie(t,s,e,o);return l(),Be(),c});return i?n.unshift(r):n.push(r),r}}const Ke=e=>(t,s=ce)=>{(!Gt||e==="sp")&&hs(e,(...i)=>t(...i),s)},Zr=Ke("bm"),En=Ke("m"),Qr=Ke("bu"),eo=Ke("u"),to=Ke("bum"),li=Ke("um"),so=Ke("sp"),io=Ke("rtg"),no=Ke("rtc");function ro(e,t=ce){hs("ec",e,t)}const oo=Symbol.for("v-ndc"),Vs=e=>e?Yn(e)?ms(e):Vs(e.parent):null,Lt=ie(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Vs(e.parent),$root:e=>Vs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Fn(e),$forceUpdate:e=>e.f||(e.f=()=>{ri(e.update)}),$nextTick:e=>e.n||(e.n=ni.bind(e.proxy)),$watch:e=>Fo.bind(e)}),ws=(e,t)=>e!==K&&!e.__isScriptSetup&&j(e,t),lo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:r,accessCache:o,type:l,appContext:c}=e;let d;if(t[0]!=="$"){const C=o[t];if(C!==void 0)switch(C){case 1:return i[t];case 2:return n[t];case 4:return s[t];case 3:return r[t]}else{if(ws(i,t))return o[t]=1,i[t];if(n!==K&&j(n,t))return o[t]=2,n[t];if((d=e.propsOptions[0])&&j(d,t))return o[t]=3,r[t];if(s!==K&&j(s,t))return o[t]=4,s[t];$s&&(o[t]=0)}}const a=Lt[t];let p,x;if(a)return t==="$attrs"&&te(e.attrs,"get",""),a(e);if((p=l.__cssModules)&&(p=p[t]))return p;if(s!==K&&j(s,t))return o[t]=4,s[t];if(x=c.config.globalProperties,j(x,t))return x[t]},set({_:e},t,s){const{data:i,setupState:n,ctx:r}=e;return ws(n,t)?(n[t]=s,!0):i!==K&&j(i,t)?(i[t]=s,!0):j(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:n,propsOptions:r}},o){let l;return!!s[o]||e!==K&&j(e,o)||ws(t,o)||(l=r[0])&&j(l,o)||j(i,o)||j(Lt,o)||j(n.config.globalProperties,o)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:j(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function bi(e){return F(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let $s=!0;function co(e){const t=Fn(e),s=e.proxy,i=e.ctx;$s=!1,t.beforeCreate&&xi(t.beforeCreate,e,"bc");const{data:n,computed:r,methods:o,watch:l,provide:c,inject:d,created:a,beforeMount:p,mounted:x,beforeUpdate:C,updated:I,activated:A,deactivated:D,beforeDestroy:V,beforeUnmount:U,destroyed:z,unmounted:P,render:B,renderTracked:_e,renderTriggered:de,errorCaptured:we,serverPrefetch:ft,expose:be,inheritAttrs:Qe,components:ut,directives:at,filters:Ct}=t;if(d&&fo(d,i,null),o)for(const q in o){const H=o[q];M(H)&&(i[q]=H.bind(s))}if(n){const q=n.call(s,s);J(q)&&(e.data=ti(q))}if($s=!0,r)for(const q in r){const H=r[q],Me=M(H)?H.bind(s,s):M(H.get)?H.get.bind(s,s):Fe,dt=!M(H)&&M(H.set)?H.set.bind(s):Fe,X=Jo({get:Me,set:dt});Object.defineProperty(i,q,{enumerable:!0,configurable:!0,get:()=>X.value,set:O=>X.value=O})}if(l)for(const q in l)On(l[q],i,s,q);if(c){const q=M(c)?c.call(s):c;Reflect.ownKeys(q).forEach(H=>{mo(H,q[H])})}a&&xi(a,e,"c");function Q(q,H){F(H)?H.forEach(Me=>q(Me.bind(s))):H&&q(H.bind(s))}if(Q(Zr,p),Q(En,x),Q(Qr,C),Q(eo,I),Q(Yr,A),Q(Jr,D),Q(ro,we),Q(no,_e),Q(io,de),Q(to,U),Q(li,P),Q(so,ft),F(be))if(be.length){const q=e.exposed||(e.exposed={});be.forEach(H=>{Object.defineProperty(q,H,{get:()=>s[H],set:Me=>s[H]=Me})})}else e.exposed||(e.exposed={});B&&e.render===Fe&&(e.render=B),Qe!=null&&(e.inheritAttrs=Qe),ut&&(e.components=ut),at&&(e.directives=at),ft&&Cn(e)}function fo(e,t,s=Fe){F(e)&&(e=Ls(e));for(const i in e){const n=e[i];let r;J(n)?"default"in n?r=es(n.from||i,n.default,!0):r=es(n.from||i):r=es(n),se(r)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>r.value,set:o=>r.value=o}):t[i]=r}}function xi(e,t,s){Ie(F(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function On(e,t,s,i){let n=i.includes(".")?Wn(s,i):()=>s[i];if(ee(e)){const r=t[e];M(r)&&Cs(n,r)}else if(M(e))Cs(n,e.bind(s));else if(J(e))if(F(e))e.forEach(r=>On(r,t,s,i));else{const r=M(e.handler)?e.handler.bind(s):t[e.handler];M(r)&&Cs(n,r,e)}}function Fn(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:n,optionsCache:r,config:{optionMergeStrategies:o}}=e.appContext,l=r.get(t);let c;return l?c=l:!n.length&&!s&&!i?c=t:(c={},n.length&&n.forEach(d=>ls(c,d,o,!0)),ls(c,t,o)),J(t)&&r.set(t,c),c}function ls(e,t,s,i=!1){const{mixins:n,extends:r}=t;r&&ls(e,r,s,!0),n&&n.forEach(o=>ls(e,o,s,!0));for(const o in t)if(!(i&&o==="expose")){const l=uo[o]||s&&s[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const uo={data:yi,props:wi,emits:wi,methods:At,computed:At,beforeCreate:re,created:re,beforeMount:re,mounted:re,beforeUpdate:re,updated:re,beforeDestroy:re,beforeUnmount:re,destroyed:re,unmounted:re,activated:re,deactivated:re,errorCaptured:re,serverPrefetch:re,components:At,directives:At,watch:ho,provide:yi,inject:ao};function yi(e,t){return t?e?function(){return ie(M(e)?e.call(this,this):e,M(t)?t.call(this,this):t)}:t:e}function ao(e,t){return At(Ls(e),Ls(t))}function Ls(e){if(F(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function re(e,t){return e?[...new Set([].concat(e,t))]:t}function At(e,t){return e?ie(Object.create(null),e,t):t}function wi(e,t){return e?F(e)&&F(t)?[...new Set([...e,...t])]:ie(Object.create(null),bi(e),bi(t??{})):t}function ho(e,t){if(!e)return t;if(!t)return e;const s=ie(Object.create(null),e);for(const i in t)s[i]=re(e[i],t[i]);return s}function An(){return{app:null,config:{isNativeTag:tr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let po=0;function go(e,t){return function(i,n=null){M(i)||(i=ie({},i)),n!=null&&!J(n)&&(n=null);const r=An(),o=new WeakSet,l=[];let c=!1;const d=r.app={_uid:po++,_component:i,_props:n,_container:null,_context:r,_instance:null,version:Xo,get config(){return r.config},set config(a){},use(a,...p){return o.has(a)||(a&&M(a.install)?(o.add(a),a.install(d,...p)):M(a)&&(o.add(a),a(d,...p))),d},mixin(a){return r.mixins.includes(a)||r.mixins.push(a),d},component(a,p){return p?(r.components[a]=p,d):r.components[a]},directive(a,p){return p?(r.directives[a]=p,d):r.directives[a]},mount(a,p,x){if(!c){const C=d._ceVNode||Ae(i,n);return C.appContext=r,x===!0?x="svg":x===!1&&(x=void 0),e(C,a,x),c=!0,d._container=a,a.__vue_app__=d,ms(C.component)}},onUnmount(a){l.push(a)},unmount(){c&&(Ie(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,p){return r.provides[a]=p,d},runWithContext(a){const p=xt;xt=d;try{return a()}finally{xt=p}}};return d}}let xt=null;function mo(e,t){if(ce){let s=ce.provides;const i=ce.parent&&ce.parent.provides;i===s&&(s=ce.provides=Object.create(i)),s[e]=t}}function es(e,t,s=!1){const i=ce||ve;if(i||xt){let n=xt?xt._context.provides:i?i.parent==null||i.ce?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&M(t)?t.call(i&&i.proxy):t}}const Rn={},In=()=>Object.create(Rn),Mn=e=>Object.getPrototypeOf(e)===Rn;function vo(e,t,s,i=!1){const n={},r=In();e.propsDefaults=Object.create(null),Dn(e,t,n,r);for(const o in e.propsOptions[0])o in n||(n[o]=void 0);s?e.props=i?n:Mr(n):e.type.props?e.props=n:e.props=r,e.attrs=r}function _o(e,t,s,i){const{props:n,attrs:r,vnode:{patchFlag:o}}=e,l=N(n),[c]=e.propsOptions;let d=!1;if((i||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let p=0;p<a.length;p++){let x=a[p];if(ps(e.emitsOptions,x))continue;const C=t[x];if(c)if(j(r,x))C!==r[x]&&(r[x]=C,d=!0);else{const I=Xe(x);n[I]=Hs(c,l,I,C,e,!1)}else C!==r[x]&&(r[x]=C,d=!0)}}}else{Dn(e,t,n,r)&&(d=!0);let a;for(const p in l)(!t||!j(t,p)&&((a=ct(p))===p||!j(t,a)))&&(c?s&&(s[p]!==void 0||s[a]!==void 0)&&(n[p]=Hs(c,l,p,void 0,e,!0)):delete n[p]);if(r!==l)for(const p in r)(!t||!j(t,p))&&(delete r[p],d=!0)}d&&je(e.attrs,"set","")}function Dn(e,t,s,i){const[n,r]=e.propsOptions;let o=!1,l;if(t)for(let c in t){if(Rt(c))continue;const d=t[c];let a;n&&j(n,a=Xe(c))?!r||!r.includes(a)?s[a]=d:(l||(l={}))[a]=d:ps(e.emitsOptions,c)||(!(c in i)||d!==i[c])&&(i[c]=d,o=!0)}if(r){const c=N(s),d=l||K;for(let a=0;a<r.length;a++){const p=r[a];s[p]=Hs(n,c,p,d[p],e,!j(d,p))}}return o}function Hs(e,t,s,i,n,r){const o=e[s];if(o!=null){const l=j(o,"default");if(l&&i===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&M(c)){const{propsDefaults:d}=n;if(s in d)i=d[s];else{const a=kt(n);i=d[s]=c.call(null,t),a()}}else i=c;n.ce&&n.ce._setProp(s,i)}o[0]&&(r&&!l?i=!1:o[1]&&(i===""||i===ct(s))&&(i=!0))}return i}const bo=new WeakMap;function Vn(e,t,s=!1){const i=s?bo:t.propsCache,n=i.get(e);if(n)return n;const r=e.props,o={},l=[];let c=!1;if(!M(e)){const a=p=>{c=!0;const[x,C]=Vn(p,t,!0);ie(o,x),C&&l.push(...C)};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!r&&!c)return J(e)&&i.set(e,vt),vt;if(F(r))for(let a=0;a<r.length;a++){const p=Xe(r[a]);Si(p)&&(o[p]=K)}else if(r)for(const a in r){const p=Xe(a);if(Si(p)){const x=r[a],C=o[p]=F(x)||M(x)?{type:x}:ie({},x),I=C.type;let A=!1,D=!0;if(F(I))for(let V=0;V<I.length;++V){const U=I[V],z=M(U)&&U.name;if(z==="Boolean"){A=!0;break}else z==="String"&&(D=!1)}else A=M(I)&&I.name==="Boolean";C[0]=A,C[1]=D,(A||j(C,"default"))&&l.push(p)}}const d=[o,l];return J(e)&&i.set(e,d),d}function Si(e){return e[0]!=="$"&&!Rt(e)}const ci=e=>e[0]==="_"||e==="$stable",fi=e=>F(e)?e.map(Oe):[Oe(e)],xo=(e,t,s)=>{if(t._n)return t;const i=zr((...n)=>fi(t(...n)),s);return i._c=!1,i},$n=(e,t,s)=>{const i=e._ctx;for(const n in e){if(ci(n))continue;const r=e[n];if(M(r))t[n]=xo(n,r,i);else if(r!=null){const o=fi(r);t[n]=()=>o}}},Ln=(e,t)=>{const s=fi(t);e.slots.default=()=>s},Hn=(e,t,s)=>{for(const i in t)(s||!ci(i))&&(e[i]=t[i])},yo=(e,t,s)=>{const i=e.slots=In();if(e.vnode.shapeFlag&32){const n=t.__;n&&Fs(i,"__",n,!0);const r=t._;r?(Hn(i,t,s),s&&Fs(i,"_",r,!0)):$n(t,i)}else t&&Ln(e,t)},wo=(e,t,s)=>{const{vnode:i,slots:n}=e;let r=!0,o=K;if(i.shapeFlag&32){const l=t._;l?s&&l===1?r=!1:Hn(n,t,s):(r=!t.$stable,$n(t,n)),o=t}else t&&(Ln(e,t),o={default:1});if(r)for(const l in n)!ci(l)&&o[l]==null&&delete n[l]},pe=$o;function So(e){return Co(e)}function Co(e,t){const s=as();s.__VUE__=!0;const{insert:i,remove:n,patchProp:r,createElement:o,createText:l,createComment:c,setText:d,setElementText:a,parentNode:p,nextSibling:x,setScopeId:C=Fe,insertStaticContent:I}=e,A=(f,u,h,v=null,g=null,m=null,w=void 0,y=null,b=!!u.dynamicChildren)=>{if(f===u)return;f&&!Ft(f,u)&&(v=et(f),O(f,g,m,!0),f=null),u.patchFlag===-2&&(b=!1,u.dynamicChildren=null);const{type:_,ref:E,shapeFlag:S}=u;switch(_){case gs:D(f,u,h,v);break;case Ze:V(f,u,h,v);break;case Ts:f==null&&U(u,h,v,w);break;case Ne:ut(f,u,h,v,g,m,w,y,b);break;default:S&1?B(f,u,h,v,g,m,w,y,b):S&6?at(f,u,h,v,g,m,w,y,b):(S&64||S&128)&&_.process(f,u,h,v,g,m,w,y,b,Tt)}E!=null&&g?Vt(E,f&&f.ref,m,u||f,!u):E==null&&f&&f.ref!=null&&Vt(f.ref,null,m,f,!0)},D=(f,u,h,v)=>{if(f==null)i(u.el=l(u.children),h,v);else{const g=u.el=f.el;u.children!==f.children&&d(g,u.children)}},V=(f,u,h,v)=>{f==null?i(u.el=c(u.children||""),h,v):u.el=f.el},U=(f,u,h,v)=>{[f.el,f.anchor]=I(f.children,u,h,v,f.el,f.anchor)},z=({el:f,anchor:u},h,v)=>{let g;for(;f&&f!==u;)g=x(f),i(f,h,v),f=g;i(u,h,v)},P=({el:f,anchor:u})=>{let h;for(;f&&f!==u;)h=x(f),n(f),f=h;n(u)},B=(f,u,h,v,g,m,w,y,b)=>{u.type==="svg"?w="svg":u.type==="math"&&(w="mathml"),f==null?_e(u,h,v,g,m,w,y,b):ft(f,u,g,m,w,y,b)},_e=(f,u,h,v,g,m,w,y)=>{let b,_;const{props:E,shapeFlag:S,transition:T,dirs:R}=f;if(b=f.el=o(f.type,m,E&&E.is,E),S&8?a(b,f.children):S&16&&we(f.children,b,null,v,g,Ss(f,m),w,y),R&&st(f,null,v,"created"),de(b,f,f.scopeId,w,v),E){for(const k in E)k!=="value"&&!Rt(k)&&r(b,k,null,E[k],m,v);"value"in E&&r(b,"value",null,E.value,m),(_=E.onVnodeBeforeMount)&&Pe(_,v,f)}R&&st(f,null,v,"beforeMount");const $=To(g,T);$&&T.beforeEnter(b),i(b,u,h),((_=E&&E.onVnodeMounted)||$||R)&&pe(()=>{_&&Pe(_,v,f),$&&T.enter(b),R&&st(f,null,v,"mounted")},g)},de=(f,u,h,v,g)=>{if(h&&C(f,h),v)for(let m=0;m<v.length;m++)C(f,v[m]);if(g){let m=g.subTree;if(u===m||Bn(m.type)&&(m.ssContent===u||m.ssFallback===u)){const w=g.vnode;de(f,w,w.scopeId,w.slotScopeIds,g.parent)}}},we=(f,u,h,v,g,m,w,y,b=0)=>{for(let _=b;_<f.length;_++){const E=f[_]=y?ke(f[_]):Oe(f[_]);A(null,E,u,h,v,g,m,w,y)}},ft=(f,u,h,v,g,m,w)=>{const y=u.el=f.el;let{patchFlag:b,dynamicChildren:_,dirs:E}=u;b|=f.patchFlag&16;const S=f.props||K,T=u.props||K;let R;if(h&&it(h,!1),(R=T.onVnodeBeforeUpdate)&&Pe(R,h,u,f),E&&st(u,f,h,"beforeUpdate"),h&&it(h,!0),(S.innerHTML&&T.innerHTML==null||S.textContent&&T.textContent==null)&&a(y,""),_?be(f.dynamicChildren,_,y,h,v,Ss(u,g),m):w||H(f,u,y,null,h,v,Ss(u,g),m,!1),b>0){if(b&16)Qe(y,S,T,h,g);else if(b&2&&S.class!==T.class&&r(y,"class",null,T.class,g),b&4&&r(y,"style",S.style,T.style,g),b&8){const $=u.dynamicProps;for(let k=0;k<$.length;k++){const W=$[k],fe=S[W],ue=T[W];(ue!==fe||W==="value")&&r(y,W,fe,ue,g,h)}}b&1&&f.children!==u.children&&a(y,u.children)}else!w&&_==null&&Qe(y,S,T,h,g);((R=T.onVnodeUpdated)||E)&&pe(()=>{R&&Pe(R,h,u,f),E&&st(u,f,h,"updated")},v)},be=(f,u,h,v,g,m,w)=>{for(let y=0;y<u.length;y++){const b=f[y],_=u[y],E=b.el&&(b.type===Ne||!Ft(b,_)||b.shapeFlag&198)?p(b.el):h;A(b,_,E,null,v,g,m,w,!0)}},Qe=(f,u,h,v,g)=>{if(u!==h){if(u!==K)for(const m in u)!Rt(m)&&!(m in h)&&r(f,m,u[m],null,g,v);for(const m in h){if(Rt(m))continue;const w=h[m],y=u[m];w!==y&&m!=="value"&&r(f,m,y,w,g,v)}"value"in h&&r(f,"value",u.value,h.value,g)}},ut=(f,u,h,v,g,m,w,y,b)=>{const _=u.el=f?f.el:l(""),E=u.anchor=f?f.anchor:l("");let{patchFlag:S,dynamicChildren:T,slotScopeIds:R}=u;R&&(y=y?y.concat(R):R),f==null?(i(_,h,v),i(E,h,v),we(u.children||[],h,E,g,m,w,y,b)):S>0&&S&64&&T&&f.dynamicChildren?(be(f.dynamicChildren,T,h,g,m,w,y),(u.key!=null||g&&u===g.subTree)&&Nn(f,u,!0)):H(f,u,h,E,g,m,w,y,b)},at=(f,u,h,v,g,m,w,y,b)=>{u.slotScopeIds=y,f==null?u.shapeFlag&512?g.ctx.activate(u,h,v,w,b):Ct(u,h,v,g,m,w,b):Yt(f,u,b)},Ct=(f,u,h,v,g,m,w)=>{const y=f.component=Bo(f,v,g);if(Tn(f)&&(y.ctx.renderer=Tt),Ko(y,!1,w),y.asyncDep){if(g&&g.registerDep(y,Q,w),!f.el){const b=y.subTree=Ae(Ze);V(null,b,u,h)}}else Q(y,f,u,h,g,m,w)},Yt=(f,u,h)=>{const v=u.component=f.component;if(Do(f,u,h))if(v.asyncDep&&!v.asyncResolved){q(v,u,h);return}else v.next=u,v.update();else u.el=f.el,v.vnode=u},Q=(f,u,h,v,g,m,w)=>{const y=()=>{if(f.isMounted){let{next:S,bu:T,u:R,parent:$,vnode:k}=f;{const Ce=jn(f);if(Ce){S&&(S.el=k.el,q(f,S,w)),Ce.asyncDep.then(()=>{f.isUnmounted||y()});return}}let W=S,fe;it(f,!1),S?(S.el=k.el,q(f,S,w)):S=k,T&&Qt(T),(fe=S.props&&S.props.onVnodeBeforeUpdate)&&Pe(fe,$,S,k),it(f,!0);const ue=Ti(f),Se=f.subTree;f.subTree=ue,A(Se,ue,p(Se.el),et(Se),f,g,m),S.el=ue.el,W===null&&Vo(f,ue.el),R&&pe(R,g),(fe=S.props&&S.props.onVnodeUpdated)&&pe(()=>Pe(fe,$,S,k),g)}else{let S;const{el:T,props:R}=u,{bm:$,m:k,parent:W,root:fe,type:ue}=f,Se=$t(u);it(f,!1),$&&Qt($),!Se&&(S=R&&R.onVnodeBeforeMount)&&Pe(S,W,u),it(f,!0);{fe.ce&&fe.ce._def.shadowRoot!==!1&&fe.ce._injectChildStyle(ue);const Ce=f.subTree=Ti(f);A(null,Ce,h,v,f,g,m),u.el=Ce.el}if(k&&pe(k,g),!Se&&(S=R&&R.onVnodeMounted)){const Ce=u;pe(()=>Pe(S,W,Ce),g)}(u.shapeFlag&256||W&&$t(W.vnode)&&W.vnode.shapeFlag&256)&&f.a&&pe(f.a,g),f.isMounted=!0,u=h=v=null}};f.scope.on();const b=f.effect=new tn(y);f.scope.off();const _=f.update=b.run.bind(b),E=f.job=b.runIfDirty.bind(b);E.i=f,E.id=f.uid,b.scheduler=()=>ri(E),it(f,!0),_()},q=(f,u,h)=>{u.component=f;const v=f.vnode.props;f.vnode=u,f.next=null,_o(f,u.props,v,h),wo(f,u.children,h),Ge(),_i(f),Be()},H=(f,u,h,v,g,m,w,y,b=!1)=>{const _=f&&f.children,E=f?f.shapeFlag:0,S=u.children,{patchFlag:T,shapeFlag:R}=u;if(T>0){if(T&128){dt(_,S,h,v,g,m,w,y,b);return}else if(T&256){Me(_,S,h,v,g,m,w,y,b);return}}R&8?(E&16&&Ve(_,g,m),S!==_&&a(h,S)):E&16?R&16?dt(_,S,h,v,g,m,w,y,b):Ve(_,g,m,!0):(E&8&&a(h,""),R&16&&we(S,h,v,g,m,w,y,b))},Me=(f,u,h,v,g,m,w,y,b)=>{f=f||vt,u=u||vt;const _=f.length,E=u.length,S=Math.min(_,E);let T;for(T=0;T<S;T++){const R=u[T]=b?ke(u[T]):Oe(u[T]);A(f[T],R,h,null,g,m,w,y,b)}_>E?Ve(f,g,m,!0,!1,S):we(u,h,v,g,m,w,y,b,S)},dt=(f,u,h,v,g,m,w,y,b)=>{let _=0;const E=u.length;let S=f.length-1,T=E-1;for(;_<=S&&_<=T;){const R=f[_],$=u[_]=b?ke(u[_]):Oe(u[_]);if(Ft(R,$))A(R,$,h,null,g,m,w,y,b);else break;_++}for(;_<=S&&_<=T;){const R=f[S],$=u[T]=b?ke(u[T]):Oe(u[T]);if(Ft(R,$))A(R,$,h,null,g,m,w,y,b);else break;S--,T--}if(_>S){if(_<=T){const R=T+1,$=R<E?u[R].el:v;for(;_<=T;)A(null,u[_]=b?ke(u[_]):Oe(u[_]),h,$,g,m,w,y,b),_++}}else if(_>T)for(;_<=S;)O(f[_],g,m,!0),_++;else{const R=_,$=_,k=new Map;for(_=$;_<=T;_++){const he=u[_]=b?ke(u[_]):Oe(u[_]);he.key!=null&&k.set(he.key,_)}let W,fe=0;const ue=T-$+1;let Se=!1,Ce=0;const Pt=new Array(ue);for(_=0;_<ue;_++)Pt[_]=0;for(_=R;_<=S;_++){const he=f[_];if(fe>=ue){O(he,g,m,!0);continue}let Te;if(he.key!=null)Te=k.get(he.key);else for(W=$;W<=T;W++)if(Pt[W-$]===0&&Ft(he,u[W])){Te=W;break}Te===void 0?O(he,g,m,!0):(Pt[Te-$]=_+1,Te>=Ce?Ce=Te:Se=!0,A(he,u[Te],h,null,g,m,w,y,b),fe++)}const di=Se?Po(Pt):vt;for(W=di.length-1,_=ue-1;_>=0;_--){const he=$+_,Te=u[he],hi=he+1<E?u[he+1].el:v;Pt[_]===0?A(null,Te,h,hi,g,m,w,y,b):Se&&(W<0||_!==di[W]?X(Te,h,hi,2):W--)}}},X=(f,u,h,v,g=null)=>{const{el:m,type:w,transition:y,children:b,shapeFlag:_}=f;if(_&6){X(f.component.subTree,u,h,v);return}if(_&128){f.suspense.move(u,h,v);return}if(_&64){w.move(f,u,h,Tt);return}if(w===Ne){i(m,u,h);for(let S=0;S<b.length;S++)X(b[S],u,h,v);i(f.anchor,u,h);return}if(w===Ts){z(f,u,h);return}if(v!==2&&_&1&&y)if(v===0)y.beforeEnter(m),i(m,u,h),pe(()=>y.enter(m),g);else{const{leave:S,delayLeave:T,afterLeave:R}=y,$=()=>{f.ctx.isUnmounted?n(m):i(m,u,h)},k=()=>{S(m,()=>{$(),R&&R()})};T?T(m,$,k):k()}else i(m,u,h)},O=(f,u,h,v=!1,g=!1)=>{const{type:m,props:w,ref:y,children:b,dynamicChildren:_,shapeFlag:E,patchFlag:S,dirs:T,cacheIndex:R}=f;if(S===-2&&(g=!1),y!=null&&(Ge(),Vt(y,null,h,f,!0),Be()),R!=null&&(u.renderCache[R]=void 0),E&256){u.ctx.deactivate(f);return}const $=E&1&&T,k=!$t(f);let W;if(k&&(W=w&&w.onVnodeBeforeUnmount)&&Pe(W,u,f),E&6)De(f.component,h,v);else{if(E&128){f.suspense.unmount(h,v);return}$&&st(f,null,u,"beforeUnmount"),E&64?f.type.remove(f,u,h,Tt,v):_&&!_.hasOnce&&(m!==Ne||S>0&&S&64)?Ve(_,u,h,!1,!0):(m===Ne&&S&384||!g&&E&16)&&Ve(b,u,h),v&&Z(f)}(k&&(W=w&&w.onVnodeUnmounted)||$)&&pe(()=>{W&&Pe(W,u,f),$&&st(f,null,u,"unmounted")},h)},Z=f=>{const{type:u,el:h,anchor:v,transition:g}=f;if(u===Ne){me(h,v);return}if(u===Ts){P(f);return}const m=()=>{n(h),g&&!g.persisted&&g.afterLeave&&g.afterLeave()};if(f.shapeFlag&1&&g&&!g.persisted){const{leave:w,delayLeave:y}=g,b=()=>w(h,m);y?y(f.el,m,b):b()}else m()},me=(f,u)=>{let h;for(;f!==u;)h=x(f),n(f),f=h;n(u)},De=(f,u,h)=>{const{bum:v,scope:g,job:m,subTree:w,um:y,m:b,a:_,parent:E,slots:{__:S}}=f;Ci(b),Ci(_),v&&Qt(v),E&&F(S)&&S.forEach(T=>{E.renderCache[T]=void 0}),g.stop(),m&&(m.flags|=8,O(w,f,u,h)),y&&pe(y,u),pe(()=>{f.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},Ve=(f,u,h,v=!1,g=!1,m=0)=>{for(let w=m;w<f.length;w++)O(f[w],u,h,v,g)},et=f=>{if(f.shapeFlag&6)return et(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const u=x(f.anchor||f.el),h=u&&u[qr];return h?x(h):u};let tt=!1;const ai=(f,u,h)=>{f==null?u._vnode&&O(u._vnode,null,null,!0):A(u._vnode||null,f,u,null,null,null,h),u._vnode=f,tt||(tt=!0,_i(),xn(),tt=!1)},Tt={p:A,um:O,m:X,r:Z,mt:Ct,mc:we,pc:H,pbc:be,n:et,o:e};return{render:ai,hydrate:void 0,createApp:go(ai)}}function Ss({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function it({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function To(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Nn(e,t,s=!1){const i=e.children,n=t.children;if(F(i)&&F(n))for(let r=0;r<i.length;r++){const o=i[r];let l=n[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=n[r]=ke(n[r]),l.el=o.el),!s&&l.patchFlag!==-2&&Nn(o,l)),l.type===gs&&(l.el=o.el),l.type===Ze&&!l.el&&(l.el=o.el)}}function Po(e){const t=e.slice(),s=[0];let i,n,r,o,l;const c=e.length;for(i=0;i<c;i++){const d=e[i];if(d!==0){if(n=s[s.length-1],e[n]<d){t[i]=n,s.push(i);continue}for(r=0,o=s.length-1;r<o;)l=r+o>>1,e[s[l]]<d?r=l+1:o=l;d<e[s[r]]&&(r>0&&(t[i]=s[r-1]),s[r]=i)}}for(r=s.length,o=s[r-1];r-- >0;)s[r]=o,o=t[o];return s}function jn(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:jn(t)}function Ci(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Eo=Symbol.for("v-scx"),Oo=()=>es(Eo);function Cs(e,t,s){return Un(e,t,s)}function Un(e,t,s=K){const{immediate:i,deep:n,flush:r,once:o}=s,l=ie({},s),c=t&&i||!t&&r!=="post";let d;if(Gt){if(r==="sync"){const C=Oo();d=C.__watcherHandles||(C.__watcherHandles=[])}else if(!c){const C=()=>{};return C.stop=Fe,C.resume=Fe,C.pause=Fe,C}}const a=ce;l.call=(C,I,A)=>Ie(C,a,I,A);let p=!1;r==="post"?l.scheduler=C=>{pe(C,a&&a.suspense)}:r!=="sync"&&(p=!0,l.scheduler=(C,I)=>{I?C():ri(C)}),l.augmentJob=C=>{t&&(C.flags|=4),p&&(C.flags|=2,a&&(C.id=a.uid,C.i=a))};const x=Wr(e,t,l);return Gt&&(d?d.push(x):c&&x()),x}function Fo(e,t,s){const i=this.proxy,n=ee(e)?e.includes(".")?Wn(i,e):()=>i[e]:e.bind(i,i);let r;M(t)?r=t:(r=t.handler,s=t);const o=kt(this),l=Un(n,r.bind(i),s);return o(),l}function Wn(e,t){const s=t.split(".");return()=>{let i=e;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const Ao=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Xe(t)}Modifiers`]||e[`${ct(t)}Modifiers`];function Ro(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||K;let n=s;const r=t.startsWith("update:"),o=r&&Ao(i,t.slice(7));o&&(o.trim&&(n=s.map(a=>ee(a)?a.trim():a)),o.number&&(n=s.map(is)));let l,c=i[l=vs(t)]||i[l=vs(Xe(t))];!c&&r&&(c=i[l=vs(ct(t))]),c&&Ie(c,e,6,n);const d=i[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ie(d,e,6,n)}}function Gn(e,t,s=!1){const i=t.emitsCache,n=i.get(e);if(n!==void 0)return n;const r=e.emits;let o={},l=!1;if(!M(e)){const c=d=>{const a=Gn(d,t,!0);a&&(l=!0,ie(o,a))};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!r&&!l?(J(e)&&i.set(e,null),null):(F(r)?r.forEach(c=>o[c]=null):ie(o,r),J(e)&&i.set(e,o),o)}function ps(e,t){return!e||!fs(t)?!1:(t=t.slice(2).replace(/Once$/,""),j(e,t[0].toLowerCase()+t.slice(1))||j(e,ct(t))||j(e,t))}function Ti(e){const{type:t,vnode:s,proxy:i,withProxy:n,propsOptions:[r],slots:o,attrs:l,emit:c,render:d,renderCache:a,props:p,data:x,setupState:C,ctx:I,inheritAttrs:A}=e,D=os(e);let V,U;try{if(s.shapeFlag&4){const P=n||i,B=P;V=Oe(d.call(B,P,a,p,C,x,I)),U=l}else{const P=t;V=Oe(P.length>1?P(p,{attrs:l,slots:o,emit:c}):P(p,null)),U=t.props?l:Io(l)}}catch(P){Ht.length=0,ds(P,e,1),V=Ae(Ze)}let z=V;if(U&&A!==!1){const P=Object.keys(U),{shapeFlag:B}=z;P.length&&B&7&&(r&&P.some(Gs)&&(U=Mo(U,r)),z=yt(z,U,!1,!0))}return s.dirs&&(z=yt(z,null,!1,!0),z.dirs=z.dirs?z.dirs.concat(s.dirs):s.dirs),s.transition&&oi(z,s.transition),V=z,os(D),V}const Io=e=>{let t;for(const s in e)(s==="class"||s==="style"||fs(s))&&((t||(t={}))[s]=e[s]);return t},Mo=(e,t)=>{const s={};for(const i in e)(!Gs(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s};function Do(e,t,s){const{props:i,children:n,component:r}=e,{props:o,children:l,patchFlag:c}=t,d=r.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&c>=0){if(c&1024)return!0;if(c&16)return i?Pi(i,o,d):!!o;if(c&8){const a=t.dynamicProps;for(let p=0;p<a.length;p++){const x=a[p];if(o[x]!==i[x]&&!ps(d,x))return!0}}}else return(n||l)&&(!l||!l.$stable)?!0:i===o?!1:i?o?Pi(i,o,d):!0:!!o;return!1}function Pi(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let n=0;n<i.length;n++){const r=i[n];if(t[r]!==e[r]&&!ps(s,r))return!0}return!1}function Vo({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const Bn=e=>e.__isSuspense;function $o(e,t){t&&t.pendingBranch?F(e)?t.effects.push(...e):t.effects.push(e):Kr(e)}const Ne=Symbol.for("v-fgt"),gs=Symbol.for("v-txt"),Ze=Symbol.for("v-cmt"),Ts=Symbol.for("v-stc"),Ht=[];let ge=null;function Le(e=!1){Ht.push(ge=e?null:[])}function Lo(){Ht.pop(),ge=Ht[Ht.length-1]||null}let Wt=1;function Ei(e,t=!1){Wt+=e,e<0&&ge&&t&&(ge.hasOnce=!0)}function Kn(e){return e.dynamicChildren=Wt>0?ge||vt:null,Lo(),Wt>0&&ge&&ge.push(e),e}function ze(e,t,s,i,n,r){return Kn(L(e,t,s,i,n,r,!0))}function Ho(e,t,s,i,n){return Kn(Ae(e,t,s,i,n,!0))}function zn(e){return e?e.__v_isVNode===!0:!1}function Ft(e,t){return e.type===t.type&&e.key===t.key}const qn=({key:e})=>e??null,ts=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ee(e)||se(e)||M(e)?{i:ve,r:e,k:t,f:!!s}:e:null);function L(e,t=null,s=null,i=0,n=null,r=e===Ne?0:1,o=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&qn(t),ref:t&&ts(t),scopeId:wn,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:ve};return l?(ui(c,s),r&128&&e.normalize(c)):s&&(c.shapeFlag|=ee(s)?8:16),Wt>0&&!o&&ge&&(c.patchFlag>0||r&6)&&c.patchFlag!==32&&ge.push(c),c}const Ae=No;function No(e,t=null,s=null,i=0,n=null,r=!1){if((!e||e===oo)&&(e=Ze),zn(e)){const l=yt(e,t,!0);return s&&ui(l,s),Wt>0&&!r&&ge&&(l.shapeFlag&6?ge[ge.indexOf(e)]=l:ge.push(l)),l.patchFlag=-2,l}if(Yo(e)&&(e=e.__vccOpts),t){t=jo(t);let{class:l,style:c}=t;l&&!ee(l)&&(t.class=qs(l)),J(c)&&(ii(c)&&!F(c)&&(c=ie({},c)),t.style=zs(c))}const o=ee(e)?1:Bn(e)?128:kr(e)?64:J(e)?4:M(e)?2:0;return L(e,t,s,i,n,o,r,!0)}function jo(e){return e?ii(e)||Mn(e)?ie({},e):e:null}function yt(e,t,s=!1,i=!1){const{props:n,ref:r,patchFlag:o,children:l,transition:c}=e,d=t?Uo(n||{},t):n,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&qn(d),ref:t&&t.ref?s&&r?F(r)?r.concat(ts(t)):[r,ts(t)]:ts(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ne?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&yt(e.ssContent),ssFallback:e.ssFallback&&yt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&i&&oi(a,c.clone(a)),a}function kn(e=" ",t=0){return Ae(gs,null,e,t)}function pt(e="",t=!1){return t?(Le(),Ho(Ze,null,e)):Ae(Ze,null,e)}function Oe(e){return e==null||typeof e=="boolean"?Ae(Ze):F(e)?Ae(Ne,null,e.slice()):zn(e)?ke(e):Ae(gs,null,String(e))}function ke(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:yt(e)}function ui(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(F(t))s=16;else if(typeof t=="object")if(i&65){const n=t.default;n&&(n._c&&(n._d=!1),ui(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!Mn(t)?t._ctx=ve:n===3&&ve&&(ve.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else M(t)?(t={default:t,_ctx:ve},s=32):(t=String(t),i&64?(s=16,t=[kn(t)]):s=8);e.children=t,e.shapeFlag|=s}function Uo(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const n in i)if(n==="class")t.class!==i.class&&(t.class=qs([t.class,i.class]));else if(n==="style")t.style=zs([t.style,i.style]);else if(fs(n)){const r=t[n],o=i[n];o&&r!==o&&!(F(r)&&r.includes(o))&&(t[n]=r?[].concat(r,o):o)}else n!==""&&(t[n]=i[n])}return t}function Pe(e,t,s,i=null){Ie(e,t,7,[s,i])}const Wo=An();let Go=0;function Bo(e,t,s){const i=e.type,n=(t?t.appContext:e.appContext)||Wo,r={uid:Go++,vnode:e,type:i,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new hr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Vn(i,n),emitsOptions:Gn(i,n),emit:null,emitted:null,propsDefaults:K,inheritAttrs:i.inheritAttrs,ctx:K,data:K,props:K,attrs:K,slots:K,refs:K,setupState:K,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=Ro.bind(null,r),e.ce&&e.ce(r),r}let ce=null,cs,Ns;{const e=as(),t=(s,i)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(i),r=>{n.length>1?n.forEach(o=>o(r)):n[0](r)}};cs=t("__VUE_INSTANCE_SETTERS__",s=>ce=s),Ns=t("__VUE_SSR_SETTERS__",s=>Gt=s)}const kt=e=>{const t=ce;return cs(e),e.scope.on(),()=>{e.scope.off(),cs(t)}},Oi=()=>{ce&&ce.scope.off(),cs(null)};function Yn(e){return e.vnode.shapeFlag&4}let Gt=!1;function Ko(e,t=!1,s=!1){t&&Ns(t);const{props:i,children:n}=e.vnode,r=Yn(e);vo(e,i,r,t),yo(e,n,s||t);const o=r?zo(e,t):void 0;return t&&Ns(!1),o}function zo(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,lo);const{setup:i}=s;if(i){Ge();const n=e.setupContext=i.length>1?ko(e):null,r=kt(e),o=qt(i,e,0,[e.props,n]),l=ki(o);if(Be(),r(),(l||e.sp)&&!$t(e)&&Cn(e),l){if(o.then(Oi,Oi),t)return o.then(c=>{Fi(e,c)}).catch(c=>{ds(c,e,0)});e.asyncDep=o}else Fi(e,o)}else Jn(e)}function Fi(e,t,s){M(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:J(t)&&(e.setupState=vn(t)),Jn(e)}function Jn(e,t,s){const i=e.type;e.render||(e.render=i.render||Fe);{const n=kt(e);Ge();try{co(e)}finally{Be(),n()}}}const qo={get(e,t){return te(e,"get",""),e[t]}};function ko(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,qo),slots:e.slots,emit:e.emit,expose:t}}function ms(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(vn(Dr(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in Lt)return Lt[s](e)},has(t,s){return s in t||s in Lt}})):e.proxy}function Yo(e){return M(e)&&"__vccOpts"in e}const Jo=(e,t)=>jr(e,t,Gt),Xo="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let js;const Ai=typeof window<"u"&&window.trustedTypes;if(Ai)try{js=Ai.createPolicy("vue",{createHTML:e=>e})}catch{}const Xn=js?e=>js.createHTML(e):e=>e,Zo="http://www.w3.org/2000/svg",Qo="http://www.w3.org/1998/Math/MathML",He=typeof document<"u"?document:null,Ri=He&&He.createElement("template"),el={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const n=t==="svg"?He.createElementNS(Zo,e):t==="mathml"?He.createElementNS(Qo,e):s?He.createElement(e,{is:s}):He.createElement(e);return e==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:e=>He.createTextNode(e),createComment:e=>He.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>He.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,n,r){const o=s?s.previousSibling:t.lastChild;if(n&&(n===r||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===r||!(n=n.nextSibling)););else{Ri.innerHTML=Xn(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const l=Ri.content;if(i==="svg"||i==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,s)}return[o?o.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},tl=Symbol("_vtc");function sl(e,t,s){const i=e[tl];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Ii=Symbol("_vod"),il=Symbol("_vsh"),nl=Symbol(""),rl=/(^|;)\s*display\s*:/;function ol(e,t,s){const i=e.style,n=ee(s);let r=!1;if(s&&!n){if(t)if(ee(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();s[l]==null&&ss(i,l,"")}else for(const o in t)s[o]==null&&ss(i,o,"");for(const o in s)o==="display"&&(r=!0),ss(i,o,s[o])}else if(n){if(t!==s){const o=i[nl];o&&(s+=";"+o),i.cssText=s,r=rl.test(s)}}else t&&e.removeAttribute("style");Ii in e&&(e[Ii]=r?i.display:"",e[il]&&(i.display="none"))}const Mi=/\s*!important$/;function ss(e,t,s){if(F(s))s.forEach(i=>ss(e,t,i));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const i=ll(e,t);Mi.test(s)?e.setProperty(ct(i),s.replace(Mi,""),"important"):e[i]=s}}const Di=["Webkit","Moz","ms"],Ps={};function ll(e,t){const s=Ps[t];if(s)return s;let i=Xe(t);if(i!=="filter"&&i in e)return Ps[t]=i;i=Xi(i);for(let n=0;n<Di.length;n++){const r=Di[n]+i;if(r in e)return Ps[t]=r}return t}const Vi="http://www.w3.org/1999/xlink";function $i(e,t,s,i,n,r=ar(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Vi,t.slice(6,t.length)):e.setAttributeNS(Vi,t,s):s==null||r&&!Zi(s)?e.removeAttribute(t):e.setAttribute(t,r?"":Re(s)?String(s):s)}function Li(e,t,s,i,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Xn(s):s);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,c=s==null?e.type==="checkbox"?"on":"":String(s);(l!==c||!("_value"in e))&&(e.value=c),s==null&&e.removeAttribute(t),e._value=s;return}let o=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=Zi(s):s==null&&l==="string"?(s="",o=!0):l==="number"&&(s=0,o=!0)}try{e[t]=s}catch{}o&&e.removeAttribute(n||t)}function Ye(e,t,s,i){e.addEventListener(t,s,i)}function cl(e,t,s,i){e.removeEventListener(t,s,i)}const Hi=Symbol("_vei");function fl(e,t,s,i,n=null){const r=e[Hi]||(e[Hi]={}),o=r[t];if(i&&o)o.value=i;else{const[l,c]=ul(t);if(i){const d=r[t]=hl(i,n);Ye(e,l,d,c)}else o&&(cl(e,l,o,c),r[t]=void 0)}}const Ni=/(?:Once|Passive|Capture)$/;function ul(e){let t;if(Ni.test(e)){t={};let i;for(;i=e.match(Ni);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ct(e.slice(2)),t]}let Es=0;const al=Promise.resolve(),dl=()=>Es||(al.then(()=>Es=0),Es=Date.now());function hl(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ie(pl(i,s.value),t,5,[i])};return s.value=e,s.attached=dl(),s}function pl(e,t){if(F(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>n=>!n._stopped&&i&&i(n))}else return t}const ji=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,gl=(e,t,s,i,n,r)=>{const o=n==="svg";t==="class"?sl(e,i,o):t==="style"?ol(e,s,i):fs(t)?Gs(t)||fl(e,t,s,i,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ml(e,t,i,o))?(Li(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&$i(e,t,i,o,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ee(i))?Li(e,Xe(t),i,r,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),$i(e,t,i,o))};function ml(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&ji(t)&&M(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return ji(t)&&ee(s)?!1:t in e}const wt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return F(t)?s=>Qt(t,s):t};function vl(e){e.target.composing=!0}function Ui(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const We=Symbol("_assign"),_l={created(e,{modifiers:{lazy:t,trim:s,number:i}},n){e[We]=wt(n);const r=i||n.props&&n.props.type==="number";Ye(e,t?"change":"input",o=>{if(o.target.composing)return;let l=e.value;s&&(l=l.trim()),r&&(l=is(l)),e[We](l)}),s&&Ye(e,"change",()=>{e.value=e.value.trim()}),t||(Ye(e,"compositionstart",vl),Ye(e,"compositionend",Ui),Ye(e,"change",Ui))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:n,number:r}},o){if(e[We]=wt(o),e.composing)return;const l=(r||e.type==="number")&&!/^0\d/.test(e.value)?is(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||n&&e.value.trim()===c)||(e.value=c))}},bl={deep:!0,created(e,t,s){e[We]=wt(s),Ye(e,"change",()=>{const i=e._modelValue,n=Bt(e),r=e.checked,o=e[We];if(F(i)){const l=ks(i,n),c=l!==-1;if(r&&!c)o(i.concat(n));else if(!r&&c){const d=[...i];d.splice(l,1),o(d)}}else if(St(i)){const l=new Set(i);r?l.add(n):l.delete(n),o(l)}else o(Zn(e,r))})},mounted:Wi,beforeUpdate(e,t,s){e[We]=wt(s),Wi(e,t,s)}};function Wi(e,{value:t,oldValue:s},i){e._modelValue=t;let n;if(F(t))n=ks(t,i.props.value)>-1;else if(St(t))n=t.has(i.props.value);else{if(t===s)return;n=zt(t,Zn(e,!0))}e.checked!==n&&(e.checked=n)}const Os={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const n=St(t);Ye(e,"change",()=>{const r=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>s?is(Bt(o)):Bt(o));e[We](e.multiple?n?new Set(r):r:r[0]),e._assigning=!0,ni(()=>{e._assigning=!1})}),e[We]=wt(i)},mounted(e,{value:t}){Gi(e,t)},beforeUpdate(e,t,s){e[We]=wt(s)},updated(e,{value:t}){e._assigning||Gi(e,t)}};function Gi(e,t){const s=e.multiple,i=F(t);if(!(s&&!i&&!St(t))){for(let n=0,r=e.options.length;n<r;n++){const o=e.options[n],l=Bt(o);if(s)if(i){const c=typeof l;c==="string"||c==="number"?o.selected=t.some(d=>String(d)===String(l)):o.selected=ks(t,l)>-1}else o.selected=t.has(l);else if(zt(Bt(o),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Bt(e){return"_value"in e?e._value:e.value}function Zn(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const xl=ie({patchProp:gl},el);let Bi;function yl(){return Bi||(Bi=So(xl))}const wl=(...e)=>{const t=yl().createApp(...e),{mount:s}=t;return t.mount=i=>{const n=Cl(i);if(!n)return;const r=t._component;!M(r)&&!r.render&&!r.template&&(r.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const o=s(n,!1,Sl(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),o},t};function Sl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Cl(e){return ee(e)?document.querySelector(e):e}class Ki{constructor(t){G(this,"buffer");G(this,"writeIndex",0);G(this,"config");G(this,"isBufferFull",!1);this.config=t,this.buffer=new Float32Array(t.bufferSize),this.buffer.fill(t.benchmark)}addDataPoint(t){const s=(t-this.config.benchmark)/this.config.amplitude;this.buffer[this.writeIndex]=s,this.writeIndex=(this.writeIndex+1)%this.config.bufferSize,this.writeIndex===0&&(this.isBufferFull=!0)}addDataPoints(t){for(const s of t)this.addDataPoint(s)}getWaveformData(t,s=0){const i=Math.min(t,this.getAvailableDataLength()),n=new Float32Array(i);let r=this.writeIndex-1-s;r<0&&(r+=this.config.bufferSize);let o=r-i+1;o<0&&(o+=this.config.bufferSize);for(let l=0;l<i;l++){const c=(o+l)%this.config.bufferSize;n[l]=this.buffer[c]}return{data:n,startIndex:o,endIndex:r,length:i}}getAvailableDataLength(){return this.isBufferFull?this.config.bufferSize:this.writeIndex}getLatestDataPoint(){if(this.writeIndex===0&&!this.isBufferFull)return this.config.benchmark/this.config.amplitude;const t=this.writeIndex===0?this.config.bufferSize-1:this.writeIndex-1;return this.buffer[t]}clear(){this.buffer.fill(this.config.benchmark/this.config.amplitude),this.writeIndex=0,this.isBufferFull=!1}getBufferUsage(){return this.getAvailableDataLength()/this.config.bufferSize}static calculateDisplayLength(t,s){return Math.floor(t*s)}static calculateTimeScale(t,s){return s/t}static calculateVoltageScale(t,s){return s/t}}class zi{constructor(t,s){G(this,"canvas");G(this,"ctx");G(this,"config");G(this,"gridCanvas",null);G(this,"gridCtx",null);G(this,"lastCanvasSize",{width:0,height:0});G(this,"pixelsPerSecond",0);G(this,"pixelsPerMV",0);G(this,"smallGridPixelsX",0);G(this,"smallGridPixelsY",0);G(this,"largeGridPixelsX",0);G(this,"largeGridPixelsY",0);this.canvas=t;const i=t.getContext("2d");if(!i)throw new Error("Failed to get 2D context from canvas");this.ctx=i,this.config=s,this.updateRenderParameters()}updateRenderParameters(){const{width:t,height:s}=this.canvas;this.pixelsPerSecond=this.config.paperSpeed*this.config.speedMultiplier/25.4*96,this.pixelsPerMV=s/(20*this.config.voltageMultiplier),this.smallGridPixelsX=this.pixelsPerSecond*this.config.smallGridTime,this.smallGridPixelsY=this.pixelsPerMV*this.config.smallGridVoltage,this.largeGridPixelsX=this.pixelsPerSecond*this.config.largeGridTime,this.largeGridPixelsY=this.pixelsPerMV*this.config.largeGridVoltage}shouldRedrawGrid(){const{width:t,height:s}=this.canvas;return!this.gridCanvas||this.lastCanvasSize.width!==t||this.lastCanvasSize.height!==s}drawGrid(){const{width:t,height:s}=this.canvas;this.gridCanvas||(this.gridCanvas=document.createElement("canvas"),this.gridCtx=this.gridCanvas.getContext("2d")),this.gridCanvas.width=t,this.gridCanvas.height=s,this.lastCanvasSize={width:t,height:s};const i=this.gridCtx;i.fillStyle=this.config.backgroundColor,i.fillRect(0,0,t,s),i.strokeStyle=this.config.gridColor,i.lineWidth=.5,i.beginPath();for(let n=0;n<=t;n+=this.smallGridPixelsX)i.moveTo(n,0),i.lineTo(n,s);for(let n=0;n<=s;n+=this.smallGridPixelsY)i.moveTo(0,n),i.lineTo(t,n);i.stroke(),i.strokeStyle=this.config.largeGridColor,i.lineWidth=1,i.beginPath();for(let n=0;n<=t;n+=this.largeGridPixelsX)i.moveTo(n,0),i.lineTo(n,s);for(let n=0;n<=s;n+=this.largeGridPixelsY)i.moveTo(0,n),i.lineTo(t,n);i.stroke(),this.drawLabels(i,t,s)}drawLabels(t,s,i){t.fillStyle=this.config.textColor,t.font=`${this.config.fontSize}px ${this.config.fontFamily}`;const n=`${this.config.paperSpeed*this.config.speedMultiplier}mm/s`,r=`${this.config.voltageMultiplier}x`;t.fillText(n,10,i-30),t.fillText(r,10,i-10),this.config.largeGridTime;for(let l=0;l<=s;l+=this.largeGridPixelsX){const c=(l/this.pixelsPerSecond).toFixed(1);t.fillText(`${c}s`,l+2,15)}const o=i/2;this.config.largeGridVoltage;for(let l=0;l<=i;l+=this.largeGridPixelsY){const c=((o-l)/this.pixelsPerMV).toFixed(0);Math.abs(parseFloat(c))<.1||t.fillText(`${c}mV`,2,l-2)}}drawWaveform(t){if(this.updateRenderParameters(),this.shouldRedrawGrid()&&this.drawGrid(),this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.gridCanvas&&this.ctx.drawImage(this.gridCanvas,0,0),t.length===0)return;const{data:s}=t,{width:i,height:n}=this.canvas,r=n/2;this.ctx.strokeStyle=this.config.waveformColor,this.ctx.lineWidth=1.5,this.ctx.beginPath();const o=i/(s.length-1),l=r-s[0]*this.pixelsPerMV;this.ctx.moveTo(0,l);for(let c=1;c<s.length;c++){const d=c*o,a=r-s[c]*this.pixelsPerMV;this.ctx.lineTo(d,a)}this.ctx.stroke()}drawWaveformRealtime(t,s=-1){this.updateRenderParameters(),this.shouldRedrawGrid()&&(this.drawGrid(),this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.gridCanvas&&this.ctx.drawImage(this.gridCanvas,0,0),s=-1);const{data:i}=t;if(i.length===0)return s;const{width:n,height:r}=this.canvas,o=r/2,l=n/(i.length-1);if(this.ctx.strokeStyle=this.config.waveformColor,this.ctx.lineWidth=1.5,s<0){this.ctx.beginPath();const c=o-i[0]*this.pixelsPerMV;this.ctx.moveTo(0,c);for(let d=1;d<i.length;d++){const a=d*l,p=o-i[d]*this.pixelsPerMV;this.ctx.lineTo(a,p)}return this.ctx.stroke(),i.length-1}if(s<i.length-1){this.ctx.beginPath();const c=s*l,d=o-i[s]*this.pixelsPerMV;this.ctx.moveTo(c,d);for(let a=s+1;a<i.length;a++){const p=a*l,x=o-i[a]*this.pixelsPerMV;this.ctx.lineTo(p,x)}this.ctx.stroke()}return i.length-1}updateConfig(t){this.config={...this.config,...t},this.updateRenderParameters(),this.lastCanvasSize={width:0,height:0}}clear(){this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.gridCanvas&&this.ctx.drawImage(this.gridCanvas,0,0)}static getDefaultConfig(){return{smallGridTime:.04,largeGridTime:.2,smallGridVoltage:1,largeGridVoltage:5,paperSpeed:50,speedMultiplier:1,voltageMultiplier:1,backgroundColor:"#000000",gridColor:"#003300",largeGridColor:"#006600",waveformColor:"#00ff00",textColor:"#ffffff",fontSize:12,fontFamily:"Arial, sans-serif"}}}class Tl{constructor(){G(this,"frameCount",0);G(this,"lastTime",performance.now());G(this,"renderTimes",[]);G(this,"dataProcessingTimes",[]);G(this,"maxSamples",60)}startRenderTiming(){const t=performance.now();return()=>{const i=performance.now()-t;this.renderTimes.push(i),this.renderTimes.length>this.maxSamples&&this.renderTimes.shift()}}startDataProcessingTiming(){const t=performance.now();return()=>{const i=performance.now()-t;this.dataProcessingTimes.push(i),this.dataProcessingTimes.length>this.maxSamples&&this.dataProcessingTimes.shift()}}updateFPS(){this.frameCount++;const t=performance.now();t-this.lastTime>=1e3&&(this.lastTime=t,this.frameCount=0)}getFPS(){const s=performance.now()-this.lastTime;return s<1e3?Math.round(this.frameCount*1e3/s):0}getAverageRenderTime(){return this.renderTimes.length===0?0:this.renderTimes.reduce((s,i)=>s+i,0)/this.renderTimes.length}getAverageDataProcessingTime(){return this.dataProcessingTimes.length===0?0:this.dataProcessingTimes.reduce((s,i)=>s+i,0)/this.dataProcessingTimes.length}getMemoryUsage(){return"memory"in performance?performance.memory.usedJSHeapSize/1024/1024:0}getMetrics(){return{fps:this.getFPS(),renderTime:this.getAverageRenderTime(),dataProcessingTime:this.getAverageDataProcessingTime(),memoryUsage:this.getMemoryUsage(),cpuUsage:0}}reset(){this.frameCount=0,this.lastTime=performance.now(),this.renderTimes=[],this.dataProcessingTimes=[]}}const rt=class rt{constructor(){G(this,"gcTimer",null)}static getInstance(){return rt.instance||(rt.instance=new rt),rt.instance}startPeriodicGC(t=3e4){this.stopPeriodicGC(),this.gcTimer=window.setInterval(()=>{this.suggestGC()},t)}stopPeriodicGC(){this.gcTimer&&(clearInterval(this.gcTimer),this.gcTimer=null)}suggestGC(){"gc"in window&&typeof window.gc=="function"&&window.gc()}checkMemoryPressure(){if("memory"in performance){const t=performance.memory,s=t.usedJSHeapSize/t.jsHeapSizeLimit;return s>.8?"high":s>.6?"medium":"low"}return"low"}};G(rt,"instance");let Us=rt;class qi{constructor(t){G(this,"config");G(this,"waveformCalculator",null);G(this,"outputTimer",null);G(this,"isOutputting",!1);this.config=t}setWaveformCalculator(t){this.waveformCalculator=t}parseCSVText(t){try{const s=t.split(`
`),i=[];let n=s.length,r=0,o=this.config.skipHeader?1:0;for(let l=o;l<s.length;l++){const c=s[l].trim();if(this.config.skipEmptyLines&&!c)continue;const d=c.split(this.config.delimiter),a=this.config.targetColumn===-1?d.length-1:this.config.targetColumn;if(a>=0&&a<d.length){const p=d[a].trim();p&&!isNaN(Number(p))&&(i.push(Number(p)),r++)}}return{success:!0,data:i,totalLines:n,validLines:r}}catch(s){return{success:!1,error:s.message}}}parseCSVTextChunked(t,s){return new Promise(i=>{const n=t.split(`
`),r=[];let o=n.length,l=0,c=0,d=this.config.skipHeader?1:0;const a=()=>{const p=Math.min(d+this.config.chunkSize,n.length);for(let x=d;x<p;x++){const C=n[x].trim();if(this.config.skipEmptyLines&&!C)continue;const I=C.split(this.config.delimiter),A=this.config.targetColumn===-1?I.length-1:this.config.targetColumn;if(A>=0&&A<I.length){const D=I[A].trim();D&&!isNaN(Number(D))&&(r.push(Number(D)),l++)}}if(c=p,s){const x=c/o;s(x)}p<n.length?(d=p,setTimeout(a,0)):i({success:!0,data:r,totalLines:o,validLines:l})};a()})}async readAndParseCSVFile(t,s){try{if(!window.electronAPI)throw new Error("Electron API not available");const i=await window.electronAPI.readCsvFile(t);return!i.success||!i.data?{success:!1,error:i.error||"Failed to read file"}:await this.parseCSVTextChunked(i.data,s)}catch(i){return{success:!1,error:i.message}}}async loadFileToCalculator(t,s){if(!this.waveformCalculator)return console.error("WaveformCalculator not set"),!1;try{s&&s(0,"读取文件中...");const i=await this.readAndParseCSVFile(t,l=>{s&&s(l*.8,"解析数据中...")});if(!i.success||!i.data)return console.error("Failed to parse CSV:",i.error),!1;s&&s(.8,"添加数据到缓冲区..."),this.waveformCalculator.clear();const n=i.data,r=1e3;let o=0;for(let l=0;l<n.length;l+=r){const c=n.slice(l,l+r);if(this.waveformCalculator.addDataPoints(c),o+=c.length,s){const d=.8+o/n.length*.2;s(d,`已添加 ${o}/${n.length} 个数据点`)}await new Promise(d=>setTimeout(d,0))}return s&&s(1,`完成！加载了 ${n.length} 个数据点`),!0}catch(i){return console.error("Error loading file to calculator:",i),!1}}async startFrequencyOutput(t,s,i){if(!this.waveformCalculator)return console.error("WaveformCalculator not set"),!1;if(this.isOutputting)return console.warn("Already outputting data"),!1;if(t.length===0)return console.warn("No data to output"),!1;this.isOutputting=!0;const n=1e3/this.config.outputFrequency;let r=0,l=performance.now();return console.log(`开始按 ${this.config.outputFrequency}Hz 频率输出数据，间隔 ${n}ms`),new Promise(c=>{const d=()=>{if(!this.isOutputting||r>=t.length){this.stopFrequencyOutput(),i&&i(),c(!0);return}const a=t[r];this.waveformCalculator&&this.waveformCalculator.addDataPoint(a),s&&s(r+1,t.length,a),r++,l+=n;const p=performance.now(),x=Math.max(0,l-p);this.outputTimer=window.setTimeout(d,x)};d(),c(!0)})}stopFrequencyOutput(){this.isOutputting=!1,this.outputTimer&&(clearTimeout(this.outputTimer),this.outputTimer=null),console.log("频率输出已停止")}isFrequencyOutputting(){return this.isOutputting}async loadFileAndStartFrequencyOutput(t,s,i,n){try{s&&s(0,"读取文件中...");const r=await this.readAndParseCSVFile(t,l=>{s&&s(l*.5,"解析数据中...")});return!r.success||!r.data?(console.error("Failed to parse CSV:",r.error),!1):(s&&s(.5,"准备按频率输出数据..."),this.waveformCalculator&&this.waveformCalculator.clear(),s&&s(.6,`开始按 ${this.config.outputFrequency}Hz 频率输出...`),await this.startFrequencyOutput(r.data,(l,c,d)=>{if(i&&i(l,c,d),s){const p=.6+l/c*.4;s(p,`输出数据: ${l}/${c} (${d})`)}},()=>{s&&s(1,`完成！按 ${this.config.outputFrequency}Hz 输出了 ${r.data.length} 个数据点`),n&&n()}))}catch(r){return console.error("Error in loadFileAndStartFrequencyOutput:",r),!1}}static getDefaultConfig(){return{chunkSize:1e3,delimiter:",",targetColumn:-1,skipEmptyLines:!0,skipHeader:!1,outputFrequency:500}}static validateCSVFormat(t,s){try{const i=t.split(`
`).slice(0,10),n=[];let r=0;for(const o of i){if(!o.trim())continue;const l=o.split(s.delimiter),c=s.targetColumn===-1?l.length-1:s.targetColumn;if(c>=0&&c<l.length){const d=l[c].trim();d&&!isNaN(Number(d))&&(n.push(Number(d)),r++)}}return r===0?{valid:!1,error:"未找到有效的数值数据"}:{valid:!0,sampleData:n}}catch(i){return{valid:!1,error:i.message}}}}const Pl={class:"ecg-display"},El={class:"controls"},Ol={class:"control-group"},Fl={key:0,class:"control-group"},Al=["disabled"],Rl={key:1,class:"control-group"},Il=["disabled"],Ml={key:2,class:"control-group"},Dl=["disabled"],Vl=["disabled"],$l={class:"control-group"},Ll={class:"control-group"},Hl={class:"status"},Nl={key:0},jl={key:1},Ul=["width","height"],Wl=Sn({__name:"ECGDisplay",setup(e){const t=ne(),s=ne(),i=ne(1200),n=ne(600),r=ne("file"),o=ne("http://localhost:8081/sse"),l=ne(!1),c=ne(!1),d=ne("就绪"),a=ne(0),p=ne(0),x=ne(1),C=ne(1),I=ne(!1),A=ne(!1);let D=null,V=null,U=null,z=null,P=-1,B=null;const _e=new Tl,de=Us.getInstance();En(async()=>{await ni(),we(),ft(),de.startPeriodicGC(3e4),window.addEventListener("resize",be),be()}),li(()=>{dt(),de.stopPeriodicGC(),window.removeEventListener("resize",be)});function we(){if(!t.value)return;const X={bufferSize:5e4,sampleRate:500,benchmark:2048,amplitude:800};D=new Ki(X);const O=zi.getDefaultConfig();O.speedMultiplier=x.value,O.voltageMultiplier=C.value,V=new zi(t.value,O);const Z=qi.getDefaultConfig();U=new qi(Z),U.setWaveformCalculator(D),d.value="已初始化"}function ft(){function X(){const O=_e.startRenderTiming();if(D&&V){const Z=Ki.calculateDisplayLength(10,500),me=D.getWaveformData(Z);me.length>0&&(P=V.drawWaveformRealtime(me,P),a.value=D.getAvailableDataLength(),p.value=D.getBufferUsage())}O(),_e.updateFPS(),z=requestAnimationFrame(X)}X()}function be(){if(!s.value)return;const X=s.value.getBoundingClientRect();i.value=X.width,n.value=X.height,P=-1}function Qe(){P=-1}function ut(){r.value==="sse"&&c.value&&Q(),d.value=`切换到${r.value==="file"?"CSV文件":"SSE推送"}模式`}async function at(){if(!(!window.electronAPI||!D||!U)){l.value=!0;try{const O=`${await window.electronAPI.getDataPath()}/waveform_714.csv`;if(I.value)d.value="准备500Hz频率输出...",A.value=!0,await U.loadFileAndStartFrequencyOutput(O,(me,De)=>{d.value=`${De} (${(me*100).toFixed(1)}%)`},(me,De,Ve)=>{a.value=me,me%100===0&&(d.value=`500Hz输出中: ${me}/${De} (当前值: ${Ve})`)},()=>{A.value=!1,d.value="500Hz频率输出完成"})||(d.value="500Hz频率输出启动失败",A.value=!1);else{d.value="加载文件中...";const Z=await window.electronAPI.readCsvFile(O);if(Z.success&&Z.data){const me=Z.data.trim().split(`
`),De=[];for(const Ve of me){const et=Ve.split(","),tt=et[et.length-1];tt&&!isNaN(Number(tt))&&De.push(Number(tt))}D.clear(),P=-1,D.addDataPoints(De),d.value=`已加载 ${De.length} 个数据点`}else d.value=`加载失败: ${Z.error}`}}catch(X){d.value=`加载错误: ${X}`,A.value=!1}finally{l.value=!1}}}function Ct(){c.value?Q():Yt()}function Yt(){if(!(!o.value||!D)){l.value=!0,d.value="连接SSE中...";try{B=new EventSource(o.value),B.onopen=()=>{c.value=!0,l.value=!1,d.value="SSE已连接"},B.onmessage=X=>{try{const O=JSON.parse(X.data);typeof O=="number"?D.addDataPoint(O):Array.isArray(O)&&D.addDataPoints(O)}catch(O){console.error("解析SSE数据失败:",O)}},B.onerror=()=>{d.value="SSE连接错误",Q()}}catch(X){d.value=`SSE连接失败: ${X}`,l.value=!1}}}function Q(){B&&(B.close(),B=null),c.value=!1,d.value="SSE已断开"}function q(){V&&(V.updateConfig({speedMultiplier:x.value,voltageMultiplier:C.value}),P=-1)}function H(){U&&(U.stopFrequencyOutput(),A.value=!1,d.value="500Hz频率输出已停止")}function Me(){A.value&&H(),D&&D.clear(),V&&V.clear(),P=-1,a.value=0,p.value=0,d.value="显示已清空"}function dt(){z&&cancelAnimationFrame(z),A.value&&H(),Q()}return(X,O)=>(Le(),ze("div",Pl,[L("div",El,[L("div",Ol,[O[6]||(O[6]=L("label",null,"数据源:",-1)),Ot(L("select",{"onUpdate:modelValue":O[0]||(O[0]=Z=>r.value=Z),onChange:ut},O[5]||(O[5]=[L("option",{value:"file"},"CSV文件",-1),L("option",{value:"sse"},"SSE推送",-1)]),544),[[Os,r.value]])]),r.value==="file"?(Le(),ze("div",Fl,[L("label",null,[Ot(L("input",{type:"checkbox","onUpdate:modelValue":O[1]||(O[1]=Z=>I.value=Z),disabled:l.value},null,8,Al),[[bl,I.value]]),O[7]||(O[7]=kn(" 500Hz频率输出 "))])])):pt("",!0),r.value==="file"?(Le(),ze("div",Rl,[L("button",{onClick:at,disabled:l.value},gt(l.value?"加载中...":I.value?"开始500Hz输出":"加载CSV文件"),9,Il),I.value&&A.value?(Le(),ze("button",{key:0,onClick:H,class:"stop-btn"}," 停止输出 ")):pt("",!0)])):pt("",!0),r.value==="sse"?(Le(),ze("div",Ml,[Ot(L("input",{"onUpdate:modelValue":O[2]||(O[2]=Z=>o.value=Z),placeholder:"SSE URL",disabled:c.value},null,8,Dl),[[_l,o.value]]),L("button",{onClick:Ct,disabled:l.value},gt(c.value?"断开连接":"连接SSE"),9,Vl)])):pt("",!0),L("div",$l,[O[9]||(O[9]=L("label",null,"走纸速度:",-1)),Ot(L("select",{"onUpdate:modelValue":O[3]||(O[3]=Z=>x.value=Z),onChange:q},O[8]||(O[8]=[L("option",{value:.25},"12.5mm/s",-1),L("option",{value:.5},"25mm/s",-1),L("option",{value:1},"50mm/s",-1),L("option",{value:2},"100mm/s",-1)]),544),[[Os,x.value]])]),L("div",Ll,[O[11]||(O[11]=L("label",null,"电压倍数:",-1)),Ot(L("select",{"onUpdate:modelValue":O[4]||(O[4]=Z=>C.value=Z),onChange:q},O[10]||(O[10]=[L("option",{value:.25},"x0.25",-1),L("option",{value:.5},"x0.5",-1),L("option",{value:1},"x1",-1),L("option",{value:2},"x2",-1)]),544),[[Os,C.value]])]),L("div",{class:"control-group"},[L("button",{onClick:Me},"清空显示")]),L("div",Hl,[L("span",null,"状态: "+gt(d.value),1),a.value>0?(Le(),ze("span",Nl,"数据点: "+gt(a.value),1)):pt("",!0),p.value>0?(Le(),ze("span",jl,"缓冲区: "+gt((p.value*100).toFixed(1))+"%",1)):pt("",!0)])]),L("div",{class:"canvas-container",ref_key:"canvasContainer",ref:s},[L("canvas",{ref_key:"canvas",ref:t,onResize:Qe,width:i.value,height:n.value},null,40,Ul)],512)]))}}),Gl=(e,t)=>{const s=e.__vccOpts||e;for(const[i,n]of t)s[i]=n;return s},Bl=Gl(Wl,[["__scopeId","data-v-ec31d4e4"]]),Kl={id:"app"},zl=Sn({__name:"App",setup(e){return(t,s)=>(Le(),ze("div",Kl,[Ae(Bl)]))}});wl(zl).mount("#app");
