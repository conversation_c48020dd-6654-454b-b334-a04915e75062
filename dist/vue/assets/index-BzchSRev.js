var qi=Object.defineProperty;var Ji=(e,t,s)=>t in e?qi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s;var z=(e,t,s)=>Ji(e,typeof t!="symbol"?t+"":t,s);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const r of i)if(r.type==="childList")for(const o of r.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function s(i){const r={};return i.integrity&&(r.integrity=i.integrity),i.referrerPolicy&&(r.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?r.credentials="include":i.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function n(i){if(i.ep)return;i.ep=!0;const r=s(i);fetch(i.href,r)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function js(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const G={},at=[],Ee=()=>{},Xi=()=>!1,is=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Us=e=>e.startsWith("onUpdate:"),te=Object.assign,Gs=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},ki=Object.prototype.hasOwnProperty,j=(e,t)=>ki.call(e,t),R=Array.isArray,dt=e=>Vt(e)==="[object Map]",rs=e=>Vt(e)==="[object Set]",dn=e=>Vt(e)==="[object Date]",I=e=>typeof e=="function",k=e=>typeof e=="string",Re=e=>typeof e=="symbol",Y=e=>e!==null&&typeof e=="object",Bn=e=>(Y(e)||I(e))&&I(e.then)&&I(e.catch),Kn=Object.prototype.toString,Vt=e=>Kn.call(e),Zi=e=>Vt(e).slice(8,-1),zn=e=>Vt(e)==="[object Object]",Ws=e=>k(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,wt=js(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),os=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Qi=/-(\w)/g,Ke=os(e=>e.replace(Qi,(t,s)=>s?s.toUpperCase():"")),er=/\B([A-Z])/g,st=os(e=>e.replace(er,"-$1").toLowerCase()),Yn=os(e=>e.charAt(0).toUpperCase()+e.slice(1)),gs=os(e=>e?`on${Yn(e)}`:""),Be=(e,t)=>!Object.is(e,t),zt=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},Es=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},Xt=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let hn;const ls=()=>hn||(hn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Bs(e){if(R(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],i=k(n)?ir(n):Bs(n);if(i)for(const r in i)t[r]=i[r]}return t}else if(k(e)||Y(e))return e}const tr=/;(?![^(]*\))/g,sr=/:([^]+)/,nr=/\/\*[^]*?\*\//g;function ir(e){const t={};return e.replace(nr,"").split(tr).forEach(s=>{if(s){const n=s.split(sr);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Ks(e){let t="";if(k(e))t=e;else if(R(e))for(let s=0;s<e.length;s++){const n=Ks(e[s]);n&&(t+=n+" ")}else if(Y(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const rr="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",or=js(rr);function qn(e){return!!e||e===""}function lr(e,t){if(e.length!==t.length)return!1;let s=!0;for(let n=0;s&&n<e.length;n++)s=cs(e[n],t[n]);return s}function cs(e,t){if(e===t)return!0;let s=dn(e),n=dn(t);if(s||n)return s&&n?e.getTime()===t.getTime():!1;if(s=Re(e),n=Re(t),s||n)return e===t;if(s=R(e),n=R(t),s||n)return s&&n?lr(e,t):!1;if(s=Y(e),n=Y(t),s||n){if(!s||!n)return!1;const i=Object.keys(e).length,r=Object.keys(t).length;if(i!==r)return!1;for(const o in e){const l=e.hasOwnProperty(o),f=t.hasOwnProperty(o);if(l&&!f||!l&&f||!cs(e[o],t[o]))return!1}}return String(e)===String(t)}function cr(e,t){return e.findIndex(s=>cs(s,t))}const Jn=e=>!!(e&&e.__v_isRef===!0),ct=e=>k(e)?e:e==null?"":R(e)||Y(e)&&(e.toString===Kn||!I(e.toString))?Jn(e)?ct(e.value):JSON.stringify(e,Xn,2):String(e),Xn=(e,t)=>Jn(t)?Xn(e,t.value):dt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,i],r)=>(s[ms(n,r)+" =>"]=i,s),{})}:rs(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>ms(s))}:Re(t)?ms(t):Y(t)&&!R(t)&&!zn(t)?String(t):t,ms=(e,t="")=>{var s;return Re(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let fe;class fr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=fe,!t&&fe&&(this.index=(fe.scopes||(fe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=fe;try{return fe=this,t()}finally{fe=s}}}on(){++this._on===1&&(this.prevScope=fe,fe=this)}off(){this._on>0&&--this._on===0&&(fe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function ur(){return fe}let B;const _s=new WeakSet;class kn{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,fe&&fe.active&&fe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,_s.has(this)&&(_s.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Qn(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,pn(this),ei(this);const t=B,s=_e;B=this,_e=!0;try{return this.fn()}finally{ti(this),B=t,_e=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)qs(t);this.deps=this.depsTail=void 0,pn(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?_s.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ms(this)&&this.run()}get dirty(){return Ms(this)}}let Zn=0,Ct,Tt;function Qn(e,t=!1){if(e.flags|=8,t){e.next=Tt,Tt=e;return}e.next=Ct,Ct=e}function zs(){Zn++}function Ys(){if(--Zn>0)return;if(Tt){let t=Tt;for(Tt=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;Ct;){let t=Ct;for(Ct=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function ei(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ti(e){let t,s=e.depsTail,n=s;for(;n;){const i=n.prevDep;n.version===-1?(n===s&&(s=i),qs(n),ar(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=i}e.deps=t,e.depsTail=s}function Ms(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(si(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function si(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Ot)||(e.globalVersion=Ot,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Ms(e))))return;e.flags|=2;const t=e.dep,s=B,n=_e;B=e,_e=!0;try{ei(e);const i=e.fn(e._value);(t.version===0||Be(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(i){throw t.version++,i}finally{B=s,_e=n,ti(e),e.flags&=-3}}function qs(e,t=!1){const{dep:s,prevSub:n,nextSub:i}=e;if(n&&(n.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let r=s.computed.deps;r;r=r.nextDep)qs(r,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function ar(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let _e=!0;const ni=[];function Ne(){ni.push(_e),_e=!1}function je(){const e=ni.pop();_e=e===void 0?!0:e}function pn(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=B;B=void 0;try{t()}finally{B=s}}}let Ot=0;class dr{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Js{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!B||!_e||B===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==B)s=this.activeLink=new dr(B,this),B.deps?(s.prevDep=B.depsTail,B.depsTail.nextDep=s,B.depsTail=s):B.deps=B.depsTail=s,ii(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=B.depsTail,s.nextDep=void 0,B.depsTail.nextDep=s,B.depsTail=s,B.deps===s&&(B.deps=n)}return s}trigger(t){this.version++,Ot++,this.notify(t)}notify(t){zs();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Ys()}}}function ii(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)ii(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Rs=new WeakMap,et=Symbol(""),As=Symbol(""),It=Symbol("");function Q(e,t,s){if(_e&&B){let n=Rs.get(e);n||Rs.set(e,n=new Map);let i=n.get(s);i||(n.set(s,i=new Js),i.map=n,i.key=s),i.track()}}function He(e,t,s,n,i,r){const o=Rs.get(e);if(!o){Ot++;return}const l=f=>{f&&f.trigger()};if(zs(),t==="clear")o.forEach(l);else{const f=R(e),d=f&&Ws(s);if(f&&s==="length"){const a=Number(n);o.forEach((p,w)=>{(w==="length"||w===It||!Re(w)&&w>=a)&&l(p)})}else switch((s!==void 0||o.has(void 0))&&l(o.get(s)),d&&l(o.get(It)),t){case"add":f?d&&l(o.get("length")):(l(o.get(et)),dt(e)&&l(o.get(As)));break;case"delete":f||(l(o.get(et)),dt(e)&&l(o.get(As)));break;case"set":dt(e)&&l(o.get(et));break}}Ys()}function lt(e){const t=N(e);return t===e?t:(Q(t,"iterate",It),ve(e)?t:t.map(ne))}function Xs(e){return Q(e=N(e),"iterate",It),e}const hr={__proto__:null,[Symbol.iterator](){return vs(this,Symbol.iterator,ne)},concat(...e){return lt(this).concat(...e.map(t=>R(t)?lt(t):t))},entries(){return vs(this,"entries",e=>(e[1]=ne(e[1]),e))},every(e,t){return De(this,"every",e,t,void 0,arguments)},filter(e,t){return De(this,"filter",e,t,s=>s.map(ne),arguments)},find(e,t){return De(this,"find",e,t,ne,arguments)},findIndex(e,t){return De(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return De(this,"findLast",e,t,ne,arguments)},findLastIndex(e,t){return De(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return De(this,"forEach",e,t,void 0,arguments)},includes(...e){return bs(this,"includes",e)},indexOf(...e){return bs(this,"indexOf",e)},join(e){return lt(this).join(e)},lastIndexOf(...e){return bs(this,"lastIndexOf",e)},map(e,t){return De(this,"map",e,t,void 0,arguments)},pop(){return xt(this,"pop")},push(...e){return xt(this,"push",e)},reduce(e,...t){return gn(this,"reduce",e,t)},reduceRight(e,...t){return gn(this,"reduceRight",e,t)},shift(){return xt(this,"shift")},some(e,t){return De(this,"some",e,t,void 0,arguments)},splice(...e){return xt(this,"splice",e)},toReversed(){return lt(this).toReversed()},toSorted(e){return lt(this).toSorted(e)},toSpliced(...e){return lt(this).toSpliced(...e)},unshift(...e){return xt(this,"unshift",e)},values(){return vs(this,"values",ne)}};function vs(e,t,s){const n=Xs(e),i=n[t]();return n!==e&&!ve(e)&&(i._next=i.next,i.next=()=>{const r=i._next();return r.value&&(r.value=s(r.value)),r}),i}const pr=Array.prototype;function De(e,t,s,n,i,r){const o=Xs(e),l=o!==e&&!ve(e),f=o[t];if(f!==pr[t]){const p=f.apply(e,r);return l?ne(p):p}let d=s;o!==e&&(l?d=function(p,w){return s.call(this,ne(p),w,e)}:s.length>2&&(d=function(p,w){return s.call(this,p,w,e)}));const a=f.call(o,d,n);return l&&i?i(a):a}function gn(e,t,s,n){const i=Xs(e);let r=s;return i!==e&&(ve(e)?s.length>3&&(r=function(o,l,f){return s.call(this,o,l,f,e)}):r=function(o,l,f){return s.call(this,o,ne(l),f,e)}),i[t](r,...n)}function bs(e,t,s){const n=N(e);Q(n,"iterate",It);const i=n[t](...s);return(i===-1||i===!1)&&en(s[0])?(s[0]=N(s[0]),n[t](...s)):i}function xt(e,t,s=[]){Ne(),zs();const n=N(e)[t].apply(e,s);return Ys(),je(),n}const gr=js("__proto__,__v_isRef,__isVue"),ri=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Re));function mr(e){Re(e)||(e=String(e));const t=N(this);return Q(t,"has",e),t.hasOwnProperty(e)}class oi{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){if(s==="__v_skip")return t.__v_skip;const i=this._isReadonly,r=this._isShallow;if(s==="__v_isReactive")return!i;if(s==="__v_isReadonly")return i;if(s==="__v_isShallow")return r;if(s==="__v_raw")return n===(i?r?Pr:ui:r?fi:ci).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const o=R(t);if(!i){let f;if(o&&(f=hr[s]))return f;if(s==="hasOwnProperty")return mr}const l=Reflect.get(t,s,ee(t)?t:n);return(Re(s)?ri.has(s):gr(s))||(i||Q(t,"get",s),r)?l:ee(l)?o&&Ws(s)?l:l.value:Y(l)?i?ai(l):Zs(l):l}}class li extends oi{constructor(t=!1){super(!1,t)}set(t,s,n,i){let r=t[s];if(!this._isShallow){const f=tt(r);if(!ve(n)&&!tt(n)&&(r=N(r),n=N(n)),!R(t)&&ee(r)&&!ee(n))return f?!1:(r.value=n,!0)}const o=R(t)&&Ws(s)?Number(s)<t.length:j(t,s),l=Reflect.set(t,s,n,ee(t)?t:i);return t===N(i)&&(o?Be(n,r)&&He(t,"set",s,n):He(t,"add",s,n)),l}deleteProperty(t,s){const n=j(t,s);t[s];const i=Reflect.deleteProperty(t,s);return i&&n&&He(t,"delete",s,void 0),i}has(t,s){const n=Reflect.has(t,s);return(!Re(s)||!ri.has(s))&&Q(t,"has",s),n}ownKeys(t){return Q(t,"iterate",R(t)?"length":et),Reflect.ownKeys(t)}}class _r extends oi{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const vr=new li,br=new _r,xr=new li(!0);const Os=e=>e,Ut=e=>Reflect.getPrototypeOf(e);function yr(e,t,s){return function(...n){const i=this.__v_raw,r=N(i),o=dt(r),l=e==="entries"||e===Symbol.iterator&&o,f=e==="keys"&&o,d=i[e](...n),a=s?Os:t?Is:ne;return!t&&Q(r,"iterate",f?As:et),{next(){const{value:p,done:w}=d.next();return w?{value:p,done:w}:{value:l?[a(p[0]),a(p[1])]:a(p),done:w}},[Symbol.iterator](){return this}}}}function Gt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Sr(e,t){const s={get(i){const r=this.__v_raw,o=N(r),l=N(i);e||(Be(i,l)&&Q(o,"get",i),Q(o,"get",l));const{has:f}=Ut(o),d=t?Os:e?Is:ne;if(f.call(o,i))return d(r.get(i));if(f.call(o,l))return d(r.get(l));r!==o&&r.get(i)},get size(){const i=this.__v_raw;return!e&&Q(N(i),"iterate",et),Reflect.get(i,"size",i)},has(i){const r=this.__v_raw,o=N(r),l=N(i);return e||(Be(i,l)&&Q(o,"has",i),Q(o,"has",l)),i===l?r.has(i):r.has(i)||r.has(l)},forEach(i,r){const o=this,l=o.__v_raw,f=N(l),d=t?Os:e?Is:ne;return!e&&Q(f,"iterate",et),l.forEach((a,p)=>i.call(r,d(a),d(p),o))}};return te(s,e?{add:Gt("add"),set:Gt("set"),delete:Gt("delete"),clear:Gt("clear")}:{add(i){!t&&!ve(i)&&!tt(i)&&(i=N(i));const r=N(this);return Ut(r).has.call(r,i)||(r.add(i),He(r,"add",i,i)),this},set(i,r){!t&&!ve(r)&&!tt(r)&&(r=N(r));const o=N(this),{has:l,get:f}=Ut(o);let d=l.call(o,i);d||(i=N(i),d=l.call(o,i));const a=f.call(o,i);return o.set(i,r),d?Be(r,a)&&He(o,"set",i,r):He(o,"add",i,r),this},delete(i){const r=N(this),{has:o,get:l}=Ut(r);let f=o.call(r,i);f||(i=N(i),f=o.call(r,i)),l&&l.call(r,i);const d=r.delete(i);return f&&He(r,"delete",i,void 0),d},clear(){const i=N(this),r=i.size!==0,o=i.clear();return r&&He(i,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(i=>{s[i]=yr(i,e,t)}),s}function ks(e,t){const s=Sr(e,t);return(n,i,r)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?n:Reflect.get(j(s,i)&&i in n?s:n,i,r)}const wr={get:ks(!1,!1)},Cr={get:ks(!1,!0)},Tr={get:ks(!0,!1)};const ci=new WeakMap,fi=new WeakMap,ui=new WeakMap,Pr=new WeakMap;function Er(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Mr(e){return e.__v_skip||!Object.isExtensible(e)?0:Er(Zi(e))}function Zs(e){return tt(e)?e:Qs(e,!1,vr,wr,ci)}function Rr(e){return Qs(e,!1,xr,Cr,fi)}function ai(e){return Qs(e,!0,br,Tr,ui)}function Qs(e,t,s,n,i){if(!Y(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=Mr(e);if(r===0)return e;const o=i.get(e);if(o)return o;const l=new Proxy(e,r===2?n:s);return i.set(e,l),l}function Pt(e){return tt(e)?Pt(e.__v_raw):!!(e&&e.__v_isReactive)}function tt(e){return!!(e&&e.__v_isReadonly)}function ve(e){return!!(e&&e.__v_isShallow)}function en(e){return e?!!e.__v_raw:!1}function N(e){const t=e&&e.__v_raw;return t?N(t):e}function Ar(e){return!j(e,"__v_skip")&&Object.isExtensible(e)&&Es(e,"__v_skip",!0),e}const ne=e=>Y(e)?Zs(e):e,Is=e=>Y(e)?ai(e):e;function ee(e){return e?e.__v_isRef===!0:!1}function de(e){return Or(e,!1)}function Or(e,t){return ee(e)?e:new Ir(e,t)}class Ir{constructor(t,s){this.dep=new Js,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:N(t),this._value=s?t:ne(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||ve(t)||tt(t);t=n?t:N(t),Be(t,s)&&(this._rawValue=t,this._value=n?t:ne(t),this.dep.trigger())}}function Fr(e){return ee(e)?e.value:e}const Dr={get:(e,t,s)=>t==="__v_raw"?e:Fr(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const i=e[t];return ee(i)&&!ee(s)?(i.value=s,!0):Reflect.set(e,t,s,n)}};function di(e){return Pt(e)?e:new Proxy(e,Dr)}class Lr{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Js(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ot-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&B!==this)return Qn(this,!0),!0}get value(){const t=this.dep.track();return si(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Vr(e,t,s=!1){let n,i;return I(e)?n=e:(n=e.get,i=e.set),new Lr(n,i,s)}const Wt={},kt=new WeakMap;let Xe;function Hr(e,t=!1,s=Xe){if(s){let n=kt.get(s);n||kt.set(s,n=[]),n.push(e)}}function $r(e,t,s=G){const{immediate:n,deep:i,once:r,scheduler:o,augmentJob:l,call:f}=s,d=M=>i?M:ve(M)||i===!1||i===0?$e(M,1):$e(M);let a,p,w,T,A=!1,F=!1;if(ee(e)?(p=()=>e.value,A=ve(e)):Pt(e)?(p=()=>d(e),A=!0):R(e)?(F=!0,A=e.some(M=>Pt(M)||ve(M)),p=()=>e.map(M=>{if(ee(M))return M.value;if(Pt(M))return d(M);if(I(M))return f?f(M,2):M()})):I(e)?t?p=f?()=>f(e,2):e:p=()=>{if(w){Ne();try{w()}finally{je()}}const M=Xe;Xe=a;try{return f?f(e,3,[T]):e(T)}finally{Xe=M}}:p=Ee,t&&i){const M=p,q=i===!0?1/0:i;p=()=>$e(M(),q)}const J=ur(),L=()=>{a.stop(),J&&J.active&&Gs(J.effects,a)};if(r&&t){const M=t;t=(...q)=>{M(...q),L()}}let $=F?new Array(e.length).fill(Wt):Wt;const K=M=>{if(!(!(a.flags&1)||!a.dirty&&!M))if(t){const q=a.run();if(i||A||(F?q.some((be,oe)=>Be(be,$[oe])):Be(q,$))){w&&w();const be=Xe;Xe=a;try{const oe=[q,$===Wt?void 0:F&&$[0]===Wt?[]:$,T];$=q,f?f(t,3,oe):t(...oe)}finally{Xe=be}}}else a.run()};return l&&l(K),a=new kn(p),a.scheduler=o?()=>o(K,!1):K,T=M=>Hr(M,!1,a),w=a.onStop=()=>{const M=kt.get(a);if(M){if(f)f(M,4);else for(const q of M)q();kt.delete(a)}},t?n?K(!0):$=a.run():o?o(K.bind(null,!0),!0):a.run(),L.pause=a.pause.bind(a),L.resume=a.resume.bind(a),L.stop=L,L}function $e(e,t=1/0,s){if(t<=0||!Y(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,ee(e))$e(e.value,t,s);else if(R(e))for(let n=0;n<e.length;n++)$e(e[n],t,s);else if(rs(e)||dt(e))e.forEach(n=>{$e(n,t,s)});else if(zn(e)){for(const n in e)$e(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&$e(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ht(e,t,s,n){try{return n?e(...n):e()}catch(i){fs(i,t,s)}}function Ae(e,t,s,n){if(I(e)){const i=Ht(e,t,s,n);return i&&Bn(i)&&i.catch(r=>{fs(r,t,s)}),i}if(R(e)){const i=[];for(let r=0;r<e.length;r++)i.push(Ae(e[r],t,s,n));return i}}function fs(e,t,s,n=!0){const i=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||G;if(t){let l=t.parent;const f=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const a=l.ec;if(a){for(let p=0;p<a.length;p++)if(a[p](e,f,d)===!1)return}l=l.parent}if(r){Ne(),Ht(r,null,10,[e,f,d]),je();return}}Nr(e,s,i,n,o)}function Nr(e,t,s,n=!0,i=!1){if(i)throw e;console.error(e)}const ie=[];let Te=-1;const ht=[];let Ge=null,ft=0;const hi=Promise.resolve();let Zt=null;function tn(e){const t=Zt||hi;return e?t.then(this?e.bind(this):e):t}function jr(e){let t=Te+1,s=ie.length;for(;t<s;){const n=t+s>>>1,i=ie[n],r=Ft(i);r<e||r===e&&i.flags&2?t=n+1:s=n}return t}function sn(e){if(!(e.flags&1)){const t=Ft(e),s=ie[ie.length-1];!s||!(e.flags&2)&&t>=Ft(s)?ie.push(e):ie.splice(jr(t),0,e),e.flags|=1,pi()}}function pi(){Zt||(Zt=hi.then(mi))}function Ur(e){R(e)?ht.push(...e):Ge&&e.id===-1?Ge.splice(ft+1,0,e):e.flags&1||(ht.push(e),e.flags|=1),pi()}function mn(e,t,s=Te+1){for(;s<ie.length;s++){const n=ie[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;ie.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function gi(e){if(ht.length){const t=[...new Set(ht)].sort((s,n)=>Ft(s)-Ft(n));if(ht.length=0,Ge){Ge.push(...t);return}for(Ge=t,ft=0;ft<Ge.length;ft++){const s=Ge[ft];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}Ge=null,ft=0}}const Ft=e=>e.id==null?e.flags&2?-1:1/0:e.id;function mi(e){try{for(Te=0;Te<ie.length;Te++){const t=ie[Te];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ht(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Te<ie.length;Te++){const t=ie[Te];t&&(t.flags&=-2)}Te=-1,ie.length=0,gi(),Zt=null,(ie.length||ht.length)&&mi()}}let ge=null,_i=null;function Qt(e){const t=ge;return ge=e,_i=e&&e.type.__scopeId||null,t}function Gr(e,t=ge,s){if(!t||e._n)return e;const n=(...i)=>{n._d&&Tn(-1);const r=Qt(t);let o;try{o=e(...i)}finally{Qt(r),n._d&&Tn(1)}return o};return n._n=!0,n._c=!0,n._d=!0,n}function Bt(e,t){if(ge===null)return e;const s=hs(ge),n=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[r,o,l,f=G]=t[i];r&&(I(r)&&(r={mounted:r,updated:r}),r.deep&&$e(o),n.push({dir:r,instance:s,value:o,oldValue:void 0,arg:l,modifiers:f}))}return e}function qe(e,t,s,n){const i=e.dirs,r=t&&t.dirs;for(let o=0;o<i.length;o++){const l=i[o];r&&(l.oldValue=r[o].value);let f=l.dir[n];f&&(Ne(),Ae(f,s,8,[e.el,l,e,t]),je())}}const Wr=Symbol("_vte"),Br=e=>e.__isTeleport;function nn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,nn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function vi(e,t){return I(e)?te({name:e.name},t,{setup:e}):e}function bi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Et(e,t,s,n,i=!1){if(R(e)){e.forEach((A,F)=>Et(A,t&&(R(t)?t[F]:t),s,n,i));return}if(Mt(n)&&!i){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&Et(e,t,s,n.component.subTree);return}const r=n.shapeFlag&4?hs(n.component):n.el,o=i?null:r,{i:l,r:f}=e,d=t&&t.r,a=l.refs===G?l.refs={}:l.refs,p=l.setupState,w=N(p),T=p===G?()=>!1:A=>j(w,A);if(d!=null&&d!==f&&(k(d)?(a[d]=null,T(d)&&(p[d]=null)):ee(d)&&(d.value=null)),I(f))Ht(f,l,12,[o,a]);else{const A=k(f),F=ee(f);if(A||F){const J=()=>{if(e.f){const L=A?T(f)?p[f]:a[f]:f.value;i?R(L)&&Gs(L,r):R(L)?L.includes(r)||L.push(r):A?(a[f]=[r],T(f)&&(p[f]=a[f])):(f.value=[r],e.k&&(a[e.k]=f.value))}else A?(a[f]=o,T(f)&&(p[f]=o)):F&&(f.value=o,e.k&&(a[e.k]=o))};o?(J.id=-1,he(J,s)):J()}}}ls().requestIdleCallback;ls().cancelIdleCallback;const Mt=e=>!!e.type.__asyncLoader,xi=e=>e.type.__isKeepAlive;function Kr(e,t){yi(e,"a",t)}function zr(e,t){yi(e,"da",t)}function yi(e,t,s=re){const n=e.__wdc||(e.__wdc=()=>{let i=s;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(us(t,n,s),s){let i=s.parent;for(;i&&i.parent;)xi(i.parent.vnode)&&Yr(n,t,s,i),i=i.parent}}function Yr(e,t,s,n){const i=us(t,e,n,!0);rn(()=>{Gs(n[t],i)},s)}function us(e,t,s=re,n=!1){if(s){const i=s[e]||(s[e]=[]),r=t.__weh||(t.__weh=(...o)=>{Ne();const l=$t(s),f=Ae(t,s,e,o);return l(),je(),f});return n?i.unshift(r):i.push(r),r}}const Ue=e=>(t,s=re)=>{(!Lt||e==="sp")&&us(e,(...n)=>t(...n),s)},qr=Ue("bm"),Si=Ue("m"),Jr=Ue("bu"),Xr=Ue("u"),kr=Ue("bum"),rn=Ue("um"),Zr=Ue("sp"),Qr=Ue("rtg"),eo=Ue("rtc");function to(e,t=re){us("ec",e,t)}const so=Symbol.for("v-ndc"),Fs=e=>e?Wi(e)?hs(e):Fs(e.parent):null,Rt=te(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Fs(e.parent),$root:e=>Fs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ci(e),$forceUpdate:e=>e.f||(e.f=()=>{sn(e.update)}),$nextTick:e=>e.n||(e.n=tn.bind(e.proxy)),$watch:e=>To.bind(e)}),xs=(e,t)=>e!==G&&!e.__isScriptSetup&&j(e,t),no={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:i,props:r,accessCache:o,type:l,appContext:f}=e;let d;if(t[0]!=="$"){const T=o[t];if(T!==void 0)switch(T){case 1:return n[t];case 2:return i[t];case 4:return s[t];case 3:return r[t]}else{if(xs(n,t))return o[t]=1,n[t];if(i!==G&&j(i,t))return o[t]=2,i[t];if((d=e.propsOptions[0])&&j(d,t))return o[t]=3,r[t];if(s!==G&&j(s,t))return o[t]=4,s[t];Ds&&(o[t]=0)}}const a=Rt[t];let p,w;if(a)return t==="$attrs"&&Q(e.attrs,"get",""),a(e);if((p=l.__cssModules)&&(p=p[t]))return p;if(s!==G&&j(s,t))return o[t]=4,s[t];if(w=f.config.globalProperties,j(w,t))return w[t]},set({_:e},t,s){const{data:n,setupState:i,ctx:r}=e;return xs(i,t)?(i[t]=s,!0):n!==G&&j(n,t)?(n[t]=s,!0):j(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:i,propsOptions:r}},o){let l;return!!s[o]||e!==G&&j(e,o)||xs(t,o)||(l=r[0])&&j(l,o)||j(n,o)||j(Rt,o)||j(i.config.globalProperties,o)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:j(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function _n(e){return R(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Ds=!0;function io(e){const t=Ci(e),s=e.proxy,n=e.ctx;Ds=!1,t.beforeCreate&&vn(t.beforeCreate,e,"bc");const{data:i,computed:r,methods:o,watch:l,provide:f,inject:d,created:a,beforeMount:p,mounted:w,beforeUpdate:T,updated:A,activated:F,deactivated:J,beforeDestroy:L,beforeUnmount:$,destroyed:K,unmounted:M,render:q,renderTracked:be,renderTriggered:oe,errorCaptured:xe,serverPrefetch:nt,expose:Oe,inheritAttrs:Ye,components:it,directives:Ie,filters:rt}=t;if(d&&ro(d,n,null),o)for(const D in o){const C=o[D];I(C)&&(n[D]=C.bind(s))}if(i){const D=i.call(s,s);Y(D)&&(e.data=Zs(D))}if(Ds=!0,r)for(const D in r){const C=r[D],X=I(C)?C.bind(s,s):I(C.get)?C.get.bind(s,s):Ee,me=!I(C)&&I(C.set)?C.set.bind(s):Ee,Fe=Yo({get:X,set:me});Object.defineProperty(n,D,{enumerable:!0,configurable:!0,get:()=>Fe.value,set:ue=>Fe.value=ue})}if(l)for(const D in l)wi(l[D],n,s,D);if(f){const D=I(f)?f.call(s):f;Reflect.ownKeys(D).forEach(C=>{ao(C,D[C])})}a&&vn(a,e,"c");function Z(D,C){R(C)?C.forEach(X=>D(X.bind(s))):C&&D(C.bind(s))}if(Z(qr,p),Z(Si,w),Z(Jr,T),Z(Xr,A),Z(Kr,F),Z(zr,J),Z(to,xe),Z(eo,be),Z(Qr,oe),Z(kr,$),Z(rn,M),Z(Zr,nt),R(Oe))if(Oe.length){const D=e.exposed||(e.exposed={});Oe.forEach(C=>{Object.defineProperty(D,C,{get:()=>s[C],set:X=>s[C]=X})})}else e.exposed||(e.exposed={});q&&e.render===Ee&&(e.render=q),Ye!=null&&(e.inheritAttrs=Ye),it&&(e.components=it),Ie&&(e.directives=Ie),nt&&bi(e)}function ro(e,t,s=Ee){R(e)&&(e=Ls(e));for(const n in e){const i=e[n];let r;Y(i)?"default"in i?r=Yt(i.from||n,i.default,!0):r=Yt(i.from||n):r=Yt(i),ee(r)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>r.value,set:o=>r.value=o}):t[n]=r}}function vn(e,t,s){Ae(R(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function wi(e,t,s,n){let i=n.includes(".")?Hi(s,n):()=>s[n];if(k(e)){const r=t[e];I(r)&&Ss(i,r)}else if(I(e))Ss(i,e.bind(s));else if(Y(e))if(R(e))e.forEach(r=>wi(r,t,s,n));else{const r=I(e.handler)?e.handler.bind(s):t[e.handler];I(r)&&Ss(i,r,e)}}function Ci(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:i,optionsCache:r,config:{optionMergeStrategies:o}}=e.appContext,l=r.get(t);let f;return l?f=l:!i.length&&!s&&!n?f=t:(f={},i.length&&i.forEach(d=>es(f,d,o,!0)),es(f,t,o)),Y(t)&&r.set(t,f),f}function es(e,t,s,n=!1){const{mixins:i,extends:r}=t;r&&es(e,r,s,!0),i&&i.forEach(o=>es(e,o,s,!0));for(const o in t)if(!(n&&o==="expose")){const l=oo[o]||s&&s[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const oo={data:bn,props:xn,emits:xn,methods:St,computed:St,beforeCreate:se,created:se,beforeMount:se,mounted:se,beforeUpdate:se,updated:se,beforeDestroy:se,beforeUnmount:se,destroyed:se,unmounted:se,activated:se,deactivated:se,errorCaptured:se,serverPrefetch:se,components:St,directives:St,watch:co,provide:bn,inject:lo};function bn(e,t){return t?e?function(){return te(I(e)?e.call(this,this):e,I(t)?t.call(this,this):t)}:t:e}function lo(e,t){return St(Ls(e),Ls(t))}function Ls(e){if(R(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function se(e,t){return e?[...new Set([].concat(e,t))]:t}function St(e,t){return e?te(Object.create(null),e,t):t}function xn(e,t){return e?R(e)&&R(t)?[...new Set([...e,...t])]:te(Object.create(null),_n(e),_n(t??{})):t}function co(e,t){if(!e)return t;if(!t)return e;const s=te(Object.create(null),e);for(const n in t)s[n]=se(e[n],t[n]);return s}function Ti(){return{app:null,config:{isNativeTag:Xi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let fo=0;function uo(e,t){return function(n,i=null){I(n)||(n=te({},n)),i!=null&&!Y(i)&&(i=null);const r=Ti(),o=new WeakSet,l=[];let f=!1;const d=r.app={_uid:fo++,_component:n,_props:i,_container:null,_context:r,_instance:null,version:qo,get config(){return r.config},set config(a){},use(a,...p){return o.has(a)||(a&&I(a.install)?(o.add(a),a.install(d,...p)):I(a)&&(o.add(a),a(d,...p))),d},mixin(a){return r.mixins.includes(a)||r.mixins.push(a),d},component(a,p){return p?(r.components[a]=p,d):r.components[a]},directive(a,p){return p?(r.directives[a]=p,d):r.directives[a]},mount(a,p,w){if(!f){const T=d._ceVNode||Me(n,i);return T.appContext=r,w===!0?w="svg":w===!1&&(w=void 0),e(T,a,w),f=!0,d._container=a,a.__vue_app__=d,hs(T.component)}},onUnmount(a){l.push(a)},unmount(){f&&(Ae(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,p){return r.provides[a]=p,d},runWithContext(a){const p=pt;pt=d;try{return a()}finally{pt=p}}};return d}}let pt=null;function ao(e,t){if(re){let s=re.provides;const n=re.parent&&re.parent.provides;n===s&&(s=re.provides=Object.create(n)),s[e]=t}}function Yt(e,t,s=!1){const n=re||ge;if(n||pt){let i=pt?pt._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return s&&I(t)?t.call(n&&n.proxy):t}}const Pi={},Ei=()=>Object.create(Pi),Mi=e=>Object.getPrototypeOf(e)===Pi;function ho(e,t,s,n=!1){const i={},r=Ei();e.propsDefaults=Object.create(null),Ri(e,t,i,r);for(const o in e.propsOptions[0])o in i||(i[o]=void 0);s?e.props=n?i:Rr(i):e.type.props?e.props=i:e.props=r,e.attrs=r}function po(e,t,s,n){const{props:i,attrs:r,vnode:{patchFlag:o}}=e,l=N(i),[f]=e.propsOptions;let d=!1;if((n||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let p=0;p<a.length;p++){let w=a[p];if(as(e.emitsOptions,w))continue;const T=t[w];if(f)if(j(r,w))T!==r[w]&&(r[w]=T,d=!0);else{const A=Ke(w);i[A]=Vs(f,l,A,T,e,!1)}else T!==r[w]&&(r[w]=T,d=!0)}}}else{Ri(e,t,i,r)&&(d=!0);let a;for(const p in l)(!t||!j(t,p)&&((a=st(p))===p||!j(t,a)))&&(f?s&&(s[p]!==void 0||s[a]!==void 0)&&(i[p]=Vs(f,l,p,void 0,e,!0)):delete i[p]);if(r!==l)for(const p in r)(!t||!j(t,p))&&(delete r[p],d=!0)}d&&He(e.attrs,"set","")}function Ri(e,t,s,n){const[i,r]=e.propsOptions;let o=!1,l;if(t)for(let f in t){if(wt(f))continue;const d=t[f];let a;i&&j(i,a=Ke(f))?!r||!r.includes(a)?s[a]=d:(l||(l={}))[a]=d:as(e.emitsOptions,f)||(!(f in n)||d!==n[f])&&(n[f]=d,o=!0)}if(r){const f=N(s),d=l||G;for(let a=0;a<r.length;a++){const p=r[a];s[p]=Vs(i,f,p,d[p],e,!j(d,p))}}return o}function Vs(e,t,s,n,i,r){const o=e[s];if(o!=null){const l=j(o,"default");if(l&&n===void 0){const f=o.default;if(o.type!==Function&&!o.skipFactory&&I(f)){const{propsDefaults:d}=i;if(s in d)n=d[s];else{const a=$t(i);n=d[s]=f.call(null,t),a()}}else n=f;i.ce&&i.ce._setProp(s,n)}o[0]&&(r&&!l?n=!1:o[1]&&(n===""||n===st(s))&&(n=!0))}return n}const go=new WeakMap;function Ai(e,t,s=!1){const n=s?go:t.propsCache,i=n.get(e);if(i)return i;const r=e.props,o={},l=[];let f=!1;if(!I(e)){const a=p=>{f=!0;const[w,T]=Ai(p,t,!0);te(o,w),T&&l.push(...T)};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!r&&!f)return Y(e)&&n.set(e,at),at;if(R(r))for(let a=0;a<r.length;a++){const p=Ke(r[a]);yn(p)&&(o[p]=G)}else if(r)for(const a in r){const p=Ke(a);if(yn(p)){const w=r[a],T=o[p]=R(w)||I(w)?{type:w}:te({},w),A=T.type;let F=!1,J=!0;if(R(A))for(let L=0;L<A.length;++L){const $=A[L],K=I($)&&$.name;if(K==="Boolean"){F=!0;break}else K==="String"&&(J=!1)}else F=I(A)&&A.name==="Boolean";T[0]=F,T[1]=J,(F||j(T,"default"))&&l.push(p)}}const d=[o,l];return Y(e)&&n.set(e,d),d}function yn(e){return e[0]!=="$"&&!wt(e)}const on=e=>e[0]==="_"||e==="$stable",ln=e=>R(e)?e.map(Pe):[Pe(e)],mo=(e,t,s)=>{if(t._n)return t;const n=Gr((...i)=>ln(t(...i)),s);return n._c=!1,n},Oi=(e,t,s)=>{const n=e._ctx;for(const i in e){if(on(i))continue;const r=e[i];if(I(r))t[i]=mo(i,r,n);else if(r!=null){const o=ln(r);t[i]=()=>o}}},Ii=(e,t)=>{const s=ln(t);e.slots.default=()=>s},Fi=(e,t,s)=>{for(const n in t)(s||!on(n))&&(e[n]=t[n])},_o=(e,t,s)=>{const n=e.slots=Ei();if(e.vnode.shapeFlag&32){const i=t.__;i&&Es(n,"__",i,!0);const r=t._;r?(Fi(n,t,s),s&&Es(n,"_",r,!0)):Oi(t,n)}else t&&Ii(e,t)},vo=(e,t,s)=>{const{vnode:n,slots:i}=e;let r=!0,o=G;if(n.shapeFlag&32){const l=t._;l?s&&l===1?r=!1:Fi(i,t,s):(r=!t.$stable,Oi(t,i)),o=t}else t&&(Ii(e,t),o={default:1});if(r)for(const l in i)!on(l)&&o[l]==null&&delete i[l]},he=Io;function bo(e){return xo(e)}function xo(e,t){const s=ls();s.__VUE__=!0;const{insert:n,remove:i,patchProp:r,createElement:o,createText:l,createComment:f,setText:d,setElementText:a,parentNode:p,nextSibling:w,setScopeId:T=Ee,insertStaticContent:A}=e,F=(c,u,h,_=null,g=null,m=null,y=void 0,x=null,b=!!u.dynamicChildren)=>{if(c===u)return;c&&!yt(c,u)&&(_=jt(c),ue(c,g,m,!0),c=null),u.patchFlag===-2&&(b=!1,u.dynamicChildren=null);const{type:v,ref:E,shapeFlag:S}=u;switch(v){case ds:J(c,u,h,_);break;case ze:L(c,u,h,_);break;case ws:c==null&&$(u,h,_,y);break;case Ve:it(c,u,h,_,g,m,y,x,b);break;default:S&1?q(c,u,h,_,g,m,y,x,b):S&6?Ie(c,u,h,_,g,m,y,x,b):(S&64||S&128)&&v.process(c,u,h,_,g,m,y,x,b,vt)}E!=null&&g?Et(E,c&&c.ref,m,u||c,!u):E==null&&c&&c.ref!=null&&Et(c.ref,null,m,c,!0)},J=(c,u,h,_)=>{if(c==null)n(u.el=l(u.children),h,_);else{const g=u.el=c.el;u.children!==c.children&&d(g,u.children)}},L=(c,u,h,_)=>{c==null?n(u.el=f(u.children||""),h,_):u.el=c.el},$=(c,u,h,_)=>{[c.el,c.anchor]=A(c.children,u,h,_,c.el,c.anchor)},K=({el:c,anchor:u},h,_)=>{let g;for(;c&&c!==u;)g=w(c),n(c,h,_),c=g;n(u,h,_)},M=({el:c,anchor:u})=>{let h;for(;c&&c!==u;)h=w(c),i(c),c=h;i(u)},q=(c,u,h,_,g,m,y,x,b)=>{u.type==="svg"?y="svg":u.type==="math"&&(y="mathml"),c==null?be(u,h,_,g,m,y,x,b):nt(c,u,g,m,y,x,b)},be=(c,u,h,_,g,m,y,x)=>{let b,v;const{props:E,shapeFlag:S,transition:P,dirs:O}=c;if(b=c.el=o(c.type,m,E&&E.is,E),S&8?a(b,c.children):S&16&&xe(c.children,b,null,_,g,ys(c,m),y,x),O&&qe(c,null,_,"created"),oe(b,c,c.scopeId,y,_),E){for(const W in E)W!=="value"&&!wt(W)&&r(b,W,null,E[W],m,_);"value"in E&&r(b,"value",null,E.value,m),(v=E.onVnodeBeforeMount)&&Ce(v,_,c)}O&&qe(c,null,_,"beforeMount");const V=yo(g,P);V&&P.beforeEnter(b),n(b,u,h),((v=E&&E.onVnodeMounted)||V||O)&&he(()=>{v&&Ce(v,_,c),V&&P.enter(b),O&&qe(c,null,_,"mounted")},g)},oe=(c,u,h,_,g)=>{if(h&&T(c,h),_)for(let m=0;m<_.length;m++)T(c,_[m]);if(g){let m=g.subTree;if(u===m||Ni(m.type)&&(m.ssContent===u||m.ssFallback===u)){const y=g.vnode;oe(c,y,y.scopeId,y.slotScopeIds,g.parent)}}},xe=(c,u,h,_,g,m,y,x,b=0)=>{for(let v=b;v<c.length;v++){const E=c[v]=x?We(c[v]):Pe(c[v]);F(null,E,u,h,_,g,m,y,x)}},nt=(c,u,h,_,g,m,y)=>{const x=u.el=c.el;let{patchFlag:b,dynamicChildren:v,dirs:E}=u;b|=c.patchFlag&16;const S=c.props||G,P=u.props||G;let O;if(h&&Je(h,!1),(O=P.onVnodeBeforeUpdate)&&Ce(O,h,u,c),E&&qe(u,c,h,"beforeUpdate"),h&&Je(h,!0),(S.innerHTML&&P.innerHTML==null||S.textContent&&P.textContent==null)&&a(x,""),v?Oe(c.dynamicChildren,v,x,h,_,ys(u,g),m):y||C(c,u,x,null,h,_,ys(u,g),m,!1),b>0){if(b&16)Ye(x,S,P,h,g);else if(b&2&&S.class!==P.class&&r(x,"class",null,P.class,g),b&4&&r(x,"style",S.style,P.style,g),b&8){const V=u.dynamicProps;for(let W=0;W<V.length;W++){const U=V[W],le=S[U],ce=P[U];(ce!==le||U==="value")&&r(x,U,le,ce,g,h)}}b&1&&c.children!==u.children&&a(x,u.children)}else!y&&v==null&&Ye(x,S,P,h,g);((O=P.onVnodeUpdated)||E)&&he(()=>{O&&Ce(O,h,u,c),E&&qe(u,c,h,"updated")},_)},Oe=(c,u,h,_,g,m,y)=>{for(let x=0;x<u.length;x++){const b=c[x],v=u[x],E=b.el&&(b.type===Ve||!yt(b,v)||b.shapeFlag&198)?p(b.el):h;F(b,v,E,null,_,g,m,y,!0)}},Ye=(c,u,h,_,g)=>{if(u!==h){if(u!==G)for(const m in u)!wt(m)&&!(m in h)&&r(c,m,u[m],null,g,_);for(const m in h){if(wt(m))continue;const y=h[m],x=u[m];y!==x&&m!=="value"&&r(c,m,x,y,g,_)}"value"in h&&r(c,"value",u.value,h.value,g)}},it=(c,u,h,_,g,m,y,x,b)=>{const v=u.el=c?c.el:l(""),E=u.anchor=c?c.anchor:l("");let{patchFlag:S,dynamicChildren:P,slotScopeIds:O}=u;O&&(x=x?x.concat(O):O),c==null?(n(v,h,_),n(E,h,_),xe(u.children||[],h,E,g,m,y,x,b)):S>0&&S&64&&P&&c.dynamicChildren?(Oe(c.dynamicChildren,P,h,g,m,y,x),(u.key!=null||g&&u===g.subTree)&&Di(c,u,!0)):C(c,u,h,E,g,m,y,x,b)},Ie=(c,u,h,_,g,m,y,x,b)=>{u.slotScopeIds=x,c==null?u.shapeFlag&512?g.ctx.activate(u,h,_,y,b):rt(u,h,_,g,m,y,b):Nt(c,u,b)},rt=(c,u,h,_,g,m,y)=>{const x=c.component=Uo(c,_,g);if(xi(c)&&(x.ctx.renderer=vt),Go(x,!1,y),x.asyncDep){if(g&&g.registerDep(x,Z,y),!c.el){const b=x.subTree=Me(ze);L(null,b,u,h)}}else Z(x,c,u,h,g,m,y)},Nt=(c,u,h)=>{const _=u.component=c.component;if(Ao(c,u,h))if(_.asyncDep&&!_.asyncResolved){D(_,u,h);return}else _.next=u,_.update();else u.el=c.el,_.vnode=u},Z=(c,u,h,_,g,m,y)=>{const x=()=>{if(c.isMounted){let{next:S,bu:P,u:O,parent:V,vnode:W}=c;{const Se=Li(c);if(Se){S&&(S.el=W.el,D(c,S,y)),Se.asyncDep.then(()=>{c.isUnmounted||x()});return}}let U=S,le;Je(c,!1),S?(S.el=W.el,D(c,S,y)):S=W,P&&zt(P),(le=S.props&&S.props.onVnodeBeforeUpdate)&&Ce(le,V,S,W),Je(c,!0);const ce=wn(c),ye=c.subTree;c.subTree=ce,F(ye,ce,p(ye.el),jt(ye),c,g,m),S.el=ce.el,U===null&&Oo(c,ce.el),O&&he(O,g),(le=S.props&&S.props.onVnodeUpdated)&&he(()=>Ce(le,V,S,W),g)}else{let S;const{el:P,props:O}=u,{bm:V,m:W,parent:U,root:le,type:ce}=c,ye=Mt(u);Je(c,!1),V&&zt(V),!ye&&(S=O&&O.onVnodeBeforeMount)&&Ce(S,U,u),Je(c,!0);{le.ce&&le.ce._def.shadowRoot!==!1&&le.ce._injectChildStyle(ce);const Se=c.subTree=wn(c);F(null,Se,h,_,c,g,m),u.el=Se.el}if(W&&he(W,g),!ye&&(S=O&&O.onVnodeMounted)){const Se=u;he(()=>Ce(S,U,Se),g)}(u.shapeFlag&256||U&&Mt(U.vnode)&&U.vnode.shapeFlag&256)&&c.a&&he(c.a,g),c.isMounted=!0,u=h=_=null}};c.scope.on();const b=c.effect=new kn(x);c.scope.off();const v=c.update=b.run.bind(b),E=c.job=b.runIfDirty.bind(b);E.i=c,E.id=c.uid,b.scheduler=()=>sn(E),Je(c,!0),v()},D=(c,u,h)=>{u.component=c;const _=c.vnode.props;c.vnode=u,c.next=null,po(c,u.props,_,h),vo(c,u.children,h),Ne(),mn(c),je()},C=(c,u,h,_,g,m,y,x,b=!1)=>{const v=c&&c.children,E=c?c.shapeFlag:0,S=u.children,{patchFlag:P,shapeFlag:O}=u;if(P>0){if(P&128){me(v,S,h,_,g,m,y,x,b);return}else if(P&256){X(v,S,h,_,g,m,y,x,b);return}}O&8?(E&16&&_t(v,g,m),S!==v&&a(h,S)):E&16?O&16?me(v,S,h,_,g,m,y,x,b):_t(v,g,m,!0):(E&8&&a(h,""),O&16&&xe(S,h,_,g,m,y,x,b))},X=(c,u,h,_,g,m,y,x,b)=>{c=c||at,u=u||at;const v=c.length,E=u.length,S=Math.min(v,E);let P;for(P=0;P<S;P++){const O=u[P]=b?We(u[P]):Pe(u[P]);F(c[P],O,h,null,g,m,y,x,b)}v>E?_t(c,g,m,!0,!1,S):xe(u,h,_,g,m,y,x,b,S)},me=(c,u,h,_,g,m,y,x,b)=>{let v=0;const E=u.length;let S=c.length-1,P=E-1;for(;v<=S&&v<=P;){const O=c[v],V=u[v]=b?We(u[v]):Pe(u[v]);if(yt(O,V))F(O,V,h,null,g,m,y,x,b);else break;v++}for(;v<=S&&v<=P;){const O=c[S],V=u[P]=b?We(u[P]):Pe(u[P]);if(yt(O,V))F(O,V,h,null,g,m,y,x,b);else break;S--,P--}if(v>S){if(v<=P){const O=P+1,V=O<E?u[O].el:_;for(;v<=P;)F(null,u[v]=b?We(u[v]):Pe(u[v]),h,V,g,m,y,x,b),v++}}else if(v>P)for(;v<=S;)ue(c[v],g,m,!0),v++;else{const O=v,V=v,W=new Map;for(v=V;v<=P;v++){const ae=u[v]=b?We(u[v]):Pe(u[v]);ae.key!=null&&W.set(ae.key,v)}let U,le=0;const ce=P-V+1;let ye=!1,Se=0;const bt=new Array(ce);for(v=0;v<ce;v++)bt[v]=0;for(v=O;v<=S;v++){const ae=c[v];if(le>=ce){ue(ae,g,m,!0);continue}let we;if(ae.key!=null)we=W.get(ae.key);else for(U=V;U<=P;U++)if(bt[U-V]===0&&yt(ae,u[U])){we=U;break}we===void 0?ue(ae,g,m,!0):(bt[we-V]=v+1,we>=Se?Se=we:ye=!0,F(ae,u[we],h,null,g,m,y,x,b),le++)}const un=ye?So(bt):at;for(U=un.length-1,v=ce-1;v>=0;v--){const ae=V+v,we=u[ae],an=ae+1<E?u[ae+1].el:_;bt[v]===0?F(null,we,h,an,g,m,y,x,b):ye&&(U<0||v!==un[U]?Fe(we,h,an,2):U--)}}},Fe=(c,u,h,_,g=null)=>{const{el:m,type:y,transition:x,children:b,shapeFlag:v}=c;if(v&6){Fe(c.component.subTree,u,h,_);return}if(v&128){c.suspense.move(u,h,_);return}if(v&64){y.move(c,u,h,vt);return}if(y===Ve){n(m,u,h);for(let S=0;S<b.length;S++)Fe(b[S],u,h,_);n(c.anchor,u,h);return}if(y===ws){K(c,u,h);return}if(_!==2&&v&1&&x)if(_===0)x.beforeEnter(m),n(m,u,h),he(()=>x.enter(m),g);else{const{leave:S,delayLeave:P,afterLeave:O}=x,V=()=>{c.ctx.isUnmounted?i(m):n(m,u,h)},W=()=>{S(m,()=>{V(),O&&O()})};P?P(m,V,W):W()}else n(m,u,h)},ue=(c,u,h,_=!1,g=!1)=>{const{type:m,props:y,ref:x,children:b,dynamicChildren:v,shapeFlag:E,patchFlag:S,dirs:P,cacheIndex:O}=c;if(S===-2&&(g=!1),x!=null&&(Ne(),Et(x,null,h,c,!0),je()),O!=null&&(u.renderCache[O]=void 0),E&256){u.ctx.deactivate(c);return}const V=E&1&&P,W=!Mt(c);let U;if(W&&(U=y&&y.onVnodeBeforeUnmount)&&Ce(U,u,c),E&6)Yi(c.component,h,_);else{if(E&128){c.suspense.unmount(h,_);return}V&&qe(c,null,u,"beforeUnmount"),E&64?c.type.remove(c,u,h,vt,_):v&&!v.hasOnce&&(m!==Ve||S>0&&S&64)?_t(v,u,h,!1,!0):(m===Ve&&S&384||!g&&E&16)&&_t(b,u,h),_&&ot(c)}(W&&(U=y&&y.onVnodeUnmounted)||V)&&he(()=>{U&&Ce(U,u,c),V&&qe(c,null,u,"unmounted")},h)},ot=c=>{const{type:u,el:h,anchor:_,transition:g}=c;if(u===Ve){zi(h,_);return}if(u===ws){M(c);return}const m=()=>{i(h),g&&!g.persisted&&g.afterLeave&&g.afterLeave()};if(c.shapeFlag&1&&g&&!g.persisted){const{leave:y,delayLeave:x}=g,b=()=>y(h,m);x?x(c.el,m,b):b()}else m()},zi=(c,u)=>{let h;for(;c!==u;)h=w(c),i(c),c=h;i(u)},Yi=(c,u,h)=>{const{bum:_,scope:g,job:m,subTree:y,um:x,m:b,a:v,parent:E,slots:{__:S}}=c;Sn(b),Sn(v),_&&zt(_),E&&R(S)&&S.forEach(P=>{E.renderCache[P]=void 0}),g.stop(),m&&(m.flags|=8,ue(y,c,u,h)),x&&he(x,u),he(()=>{c.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},_t=(c,u,h,_=!1,g=!1,m=0)=>{for(let y=m;y<c.length;y++)ue(c[y],u,h,_,g)},jt=c=>{if(c.shapeFlag&6)return jt(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const u=w(c.anchor||c.el),h=u&&u[Wr];return h?w(h):u};let ps=!1;const fn=(c,u,h)=>{c==null?u._vnode&&ue(u._vnode,null,null,!0):F(u._vnode||null,c,u,null,null,null,h),u._vnode=c,ps||(ps=!0,mn(),gi(),ps=!1)},vt={p:F,um:ue,m:Fe,r:ot,mt:rt,mc:xe,pc:C,pbc:Oe,n:jt,o:e};return{render:fn,hydrate:void 0,createApp:uo(fn)}}function ys({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function Je({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function yo(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Di(e,t,s=!1){const n=e.children,i=t.children;if(R(n)&&R(i))for(let r=0;r<n.length;r++){const o=n[r];let l=i[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=i[r]=We(i[r]),l.el=o.el),!s&&l.patchFlag!==-2&&Di(o,l)),l.type===ds&&(l.el=o.el),l.type===ze&&!l.el&&(l.el=o.el)}}function So(e){const t=e.slice(),s=[0];let n,i,r,o,l;const f=e.length;for(n=0;n<f;n++){const d=e[n];if(d!==0){if(i=s[s.length-1],e[i]<d){t[n]=i,s.push(n);continue}for(r=0,o=s.length-1;r<o;)l=r+o>>1,e[s[l]]<d?r=l+1:o=l;d<e[s[r]]&&(r>0&&(t[n]=s[r-1]),s[r]=n)}}for(r=s.length,o=s[r-1];r-- >0;)s[r]=o,o=t[o];return s}function Li(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Li(t)}function Sn(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const wo=Symbol.for("v-scx"),Co=()=>Yt(wo);function Ss(e,t,s){return Vi(e,t,s)}function Vi(e,t,s=G){const{immediate:n,deep:i,flush:r,once:o}=s,l=te({},s),f=t&&n||!t&&r!=="post";let d;if(Lt){if(r==="sync"){const T=Co();d=T.__watcherHandles||(T.__watcherHandles=[])}else if(!f){const T=()=>{};return T.stop=Ee,T.resume=Ee,T.pause=Ee,T}}const a=re;l.call=(T,A,F)=>Ae(T,a,A,F);let p=!1;r==="post"?l.scheduler=T=>{he(T,a&&a.suspense)}:r!=="sync"&&(p=!0,l.scheduler=(T,A)=>{A?T():sn(T)}),l.augmentJob=T=>{t&&(T.flags|=4),p&&(T.flags|=2,a&&(T.id=a.uid,T.i=a))};const w=$r(e,t,l);return Lt&&(d?d.push(w):f&&w()),w}function To(e,t,s){const n=this.proxy,i=k(e)?e.includes(".")?Hi(n,e):()=>n[e]:e.bind(n,n);let r;I(t)?r=t:(r=t.handler,s=t);const o=$t(this),l=Vi(i,r.bind(n),s);return o(),l}function Hi(e,t){const s=t.split(".");return()=>{let n=e;for(let i=0;i<s.length&&n;i++)n=n[s[i]];return n}}const Po=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ke(t)}Modifiers`]||e[`${st(t)}Modifiers`];function Eo(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||G;let i=s;const r=t.startsWith("update:"),o=r&&Po(n,t.slice(7));o&&(o.trim&&(i=s.map(a=>k(a)?a.trim():a)),o.number&&(i=s.map(Xt)));let l,f=n[l=gs(t)]||n[l=gs(Ke(t))];!f&&r&&(f=n[l=gs(st(t))]),f&&Ae(f,e,6,i);const d=n[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ae(d,e,6,i)}}function $i(e,t,s=!1){const n=t.emitsCache,i=n.get(e);if(i!==void 0)return i;const r=e.emits;let o={},l=!1;if(!I(e)){const f=d=>{const a=$i(d,t,!0);a&&(l=!0,te(o,a))};!s&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!r&&!l?(Y(e)&&n.set(e,null),null):(R(r)?r.forEach(f=>o[f]=null):te(o,r),Y(e)&&n.set(e,o),o)}function as(e,t){return!e||!is(t)?!1:(t=t.slice(2).replace(/Once$/,""),j(e,t[0].toLowerCase()+t.slice(1))||j(e,st(t))||j(e,t))}function wn(e){const{type:t,vnode:s,proxy:n,withProxy:i,propsOptions:[r],slots:o,attrs:l,emit:f,render:d,renderCache:a,props:p,data:w,setupState:T,ctx:A,inheritAttrs:F}=e,J=Qt(e);let L,$;try{if(s.shapeFlag&4){const M=i||n,q=M;L=Pe(d.call(q,M,a,p,T,w,A)),$=l}else{const M=t;L=Pe(M.length>1?M(p,{attrs:l,slots:o,emit:f}):M(p,null)),$=t.props?l:Mo(l)}}catch(M){At.length=0,fs(M,e,1),L=Me(ze)}let K=L;if($&&F!==!1){const M=Object.keys($),{shapeFlag:q}=K;M.length&&q&7&&(r&&M.some(Us)&&($=Ro($,r)),K=mt(K,$,!1,!0))}return s.dirs&&(K=mt(K,null,!1,!0),K.dirs=K.dirs?K.dirs.concat(s.dirs):s.dirs),s.transition&&nn(K,s.transition),L=K,Qt(J),L}const Mo=e=>{let t;for(const s in e)(s==="class"||s==="style"||is(s))&&((t||(t={}))[s]=e[s]);return t},Ro=(e,t)=>{const s={};for(const n in e)(!Us(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function Ao(e,t,s){const{props:n,children:i,component:r}=e,{props:o,children:l,patchFlag:f}=t,d=r.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&f>=0){if(f&1024)return!0;if(f&16)return n?Cn(n,o,d):!!o;if(f&8){const a=t.dynamicProps;for(let p=0;p<a.length;p++){const w=a[p];if(o[w]!==n[w]&&!as(d,w))return!0}}}else return(i||l)&&(!l||!l.$stable)?!0:n===o?!1:n?o?Cn(n,o,d):!0:!!o;return!1}function Cn(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let i=0;i<n.length;i++){const r=n[i];if(t[r]!==e[r]&&!as(s,r))return!0}return!1}function Oo({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const Ni=e=>e.__isSuspense;function Io(e,t){t&&t.pendingBranch?R(e)?t.effects.push(...e):t.effects.push(e):Ur(e)}const Ve=Symbol.for("v-fgt"),ds=Symbol.for("v-txt"),ze=Symbol.for("v-cmt"),ws=Symbol.for("v-stc"),At=[];let pe=null;function ke(e=!1){At.push(pe=e?null:[])}function Fo(){At.pop(),pe=At[At.length-1]||null}let Dt=1;function Tn(e,t=!1){Dt+=e,e<0&&pe&&t&&(pe.hasOnce=!0)}function ji(e){return e.dynamicChildren=Dt>0?pe||at:null,Fo(),Dt>0&&pe&&pe.push(e),e}function ut(e,t,s,n,i,r){return ji(H(e,t,s,n,i,r,!0))}function Do(e,t,s,n,i){return ji(Me(e,t,s,n,i,!0))}function Ui(e){return e?e.__v_isVNode===!0:!1}function yt(e,t){return e.type===t.type&&e.key===t.key}const Gi=({key:e})=>e??null,qt=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?k(e)||ee(e)||I(e)?{i:ge,r:e,k:t,f:!!s}:e:null);function H(e,t=null,s=null,n=0,i=null,r=e===Ve?0:1,o=!1,l=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Gi(t),ref:t&&qt(t),scopeId:_i,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:n,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:ge};return l?(cn(f,s),r&128&&e.normalize(f)):s&&(f.shapeFlag|=k(s)?8:16),Dt>0&&!o&&pe&&(f.patchFlag>0||r&6)&&f.patchFlag!==32&&pe.push(f),f}const Me=Lo;function Lo(e,t=null,s=null,n=0,i=null,r=!1){if((!e||e===so)&&(e=ze),Ui(e)){const l=mt(e,t,!0);return s&&cn(l,s),Dt>0&&!r&&pe&&(l.shapeFlag&6?pe[pe.indexOf(e)]=l:pe.push(l)),l.patchFlag=-2,l}if(zo(e)&&(e=e.__vccOpts),t){t=Vo(t);let{class:l,style:f}=t;l&&!k(l)&&(t.class=Ks(l)),Y(f)&&(en(f)&&!R(f)&&(f=te({},f)),t.style=Bs(f))}const o=k(e)?1:Ni(e)?128:Br(e)?64:Y(e)?4:I(e)?2:0;return H(e,t,s,n,i,o,r,!0)}function Vo(e){return e?en(e)||Mi(e)?te({},e):e:null}function mt(e,t,s=!1,n=!1){const{props:i,ref:r,patchFlag:o,children:l,transition:f}=e,d=t?$o(i||{},t):i,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&Gi(d),ref:t&&t.ref?s&&r?R(r)?r.concat(qt(t)):[r,qt(t)]:qt(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ve?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&mt(e.ssContent),ssFallback:e.ssFallback&&mt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&n&&nn(a,f.clone(a)),a}function Ho(e=" ",t=0){return Me(ds,null,e,t)}function Kt(e="",t=!1){return t?(ke(),Do(ze,null,e)):Me(ze,null,e)}function Pe(e){return e==null||typeof e=="boolean"?Me(ze):R(e)?Me(Ve,null,e.slice()):Ui(e)?We(e):Me(ds,null,String(e))}function We(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:mt(e)}function cn(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(R(t))s=16;else if(typeof t=="object")if(n&65){const i=t.default;i&&(i._c&&(i._d=!1),cn(e,i()),i._c&&(i._d=!0));return}else{s=32;const i=t._;!i&&!Mi(t)?t._ctx=ge:i===3&&ge&&(ge.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else I(t)?(t={default:t,_ctx:ge},s=32):(t=String(t),n&64?(s=16,t=[Ho(t)]):s=8);e.children=t,e.shapeFlag|=s}function $o(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const i in n)if(i==="class")t.class!==n.class&&(t.class=Ks([t.class,n.class]));else if(i==="style")t.style=Bs([t.style,n.style]);else if(is(i)){const r=t[i],o=n[i];o&&r!==o&&!(R(r)&&r.includes(o))&&(t[i]=r?[].concat(r,o):o)}else i!==""&&(t[i]=n[i])}return t}function Ce(e,t,s,n=null){Ae(e,t,7,[s,n])}const No=Ti();let jo=0;function Uo(e,t,s){const n=e.type,i=(t?t.appContext:e.appContext)||No,r={uid:jo++,vnode:e,type:n,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new fr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ai(n,i),emitsOptions:$i(n,i),emit:null,emitted:null,propsDefaults:G,inheritAttrs:n.inheritAttrs,ctx:G,data:G,props:G,attrs:G,slots:G,refs:G,setupState:G,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=Eo.bind(null,r),e.ce&&e.ce(r),r}let re=null,ts,Hs;{const e=ls(),t=(s,n)=>{let i;return(i=e[s])||(i=e[s]=[]),i.push(n),r=>{i.length>1?i.forEach(o=>o(r)):i[0](r)}};ts=t("__VUE_INSTANCE_SETTERS__",s=>re=s),Hs=t("__VUE_SSR_SETTERS__",s=>Lt=s)}const $t=e=>{const t=re;return ts(e),e.scope.on(),()=>{e.scope.off(),ts(t)}},Pn=()=>{re&&re.scope.off(),ts(null)};function Wi(e){return e.vnode.shapeFlag&4}let Lt=!1;function Go(e,t=!1,s=!1){t&&Hs(t);const{props:n,children:i}=e.vnode,r=Wi(e);ho(e,n,r,t),_o(e,i,s||t);const o=r?Wo(e,t):void 0;return t&&Hs(!1),o}function Wo(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,no);const{setup:n}=s;if(n){Ne();const i=e.setupContext=n.length>1?Ko(e):null,r=$t(e),o=Ht(n,e,0,[e.props,i]),l=Bn(o);if(je(),r(),(l||e.sp)&&!Mt(e)&&bi(e),l){if(o.then(Pn,Pn),t)return o.then(f=>{En(e,f)}).catch(f=>{fs(f,e,0)});e.asyncDep=o}else En(e,o)}else Bi(e)}function En(e,t,s){I(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Y(t)&&(e.setupState=di(t)),Bi(e)}function Bi(e,t,s){const n=e.type;e.render||(e.render=n.render||Ee);{const i=$t(e);Ne();try{io(e)}finally{je(),i()}}}const Bo={get(e,t){return Q(e,"get",""),e[t]}};function Ko(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,Bo),slots:e.slots,emit:e.emit,expose:t}}function hs(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(di(Ar(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in Rt)return Rt[s](e)},has(t,s){return s in t||s in Rt}})):e.proxy}function zo(e){return I(e)&&"__vccOpts"in e}const Yo=(e,t)=>Vr(e,t,Lt),qo="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let $s;const Mn=typeof window<"u"&&window.trustedTypes;if(Mn)try{$s=Mn.createPolicy("vue",{createHTML:e=>e})}catch{}const Ki=$s?e=>$s.createHTML(e):e=>e,Jo="http://www.w3.org/2000/svg",Xo="http://www.w3.org/1998/Math/MathML",Le=typeof document<"u"?document:null,Rn=Le&&Le.createElement("template"),ko={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const i=t==="svg"?Le.createElementNS(Jo,e):t==="mathml"?Le.createElementNS(Xo,e):s?Le.createElement(e,{is:s}):Le.createElement(e);return e==="select"&&n&&n.multiple!=null&&i.setAttribute("multiple",n.multiple),i},createText:e=>Le.createTextNode(e),createComment:e=>Le.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Le.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,i,r){const o=s?s.previousSibling:t.lastChild;if(i&&(i===r||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),s),!(i===r||!(i=i.nextSibling)););else{Rn.innerHTML=Ki(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const l=Rn.content;if(n==="svg"||n==="mathml"){const f=l.firstChild;for(;f.firstChild;)l.appendChild(f.firstChild);l.removeChild(f)}t.insertBefore(l,s)}return[o?o.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Zo=Symbol("_vtc");function Qo(e,t,s){const n=e[Zo];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const An=Symbol("_vod"),el=Symbol("_vsh"),tl=Symbol(""),sl=/(^|;)\s*display\s*:/;function nl(e,t,s){const n=e.style,i=k(s);let r=!1;if(s&&!i){if(t)if(k(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();s[l]==null&&Jt(n,l,"")}else for(const o in t)s[o]==null&&Jt(n,o,"");for(const o in s)o==="display"&&(r=!0),Jt(n,o,s[o])}else if(i){if(t!==s){const o=n[tl];o&&(s+=";"+o),n.cssText=s,r=sl.test(s)}}else t&&e.removeAttribute("style");An in e&&(e[An]=r?n.display:"",e[el]&&(n.display="none"))}const On=/\s*!important$/;function Jt(e,t,s){if(R(s))s.forEach(n=>Jt(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=il(e,t);On.test(s)?e.setProperty(st(n),s.replace(On,""),"important"):e[n]=s}}const In=["Webkit","Moz","ms"],Cs={};function il(e,t){const s=Cs[t];if(s)return s;let n=Ke(t);if(n!=="filter"&&n in e)return Cs[t]=n;n=Yn(n);for(let i=0;i<In.length;i++){const r=In[i]+n;if(r in e)return Cs[t]=r}return t}const Fn="http://www.w3.org/1999/xlink";function Dn(e,t,s,n,i,r=or(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Fn,t.slice(6,t.length)):e.setAttributeNS(Fn,t,s):s==null||r&&!qn(s)?e.removeAttribute(t):e.setAttribute(t,r?"":Re(s)?String(s):s)}function Ln(e,t,s,n,i){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Ki(s):s);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,f=s==null?e.type==="checkbox"?"on":"":String(s);(l!==f||!("_value"in e))&&(e.value=f),s==null&&e.removeAttribute(t),e._value=s;return}let o=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=qn(s):s==null&&l==="string"?(s="",o=!0):l==="number"&&(s=0,o=!0)}try{e[t]=s}catch{}o&&e.removeAttribute(i||t)}function Ze(e,t,s,n){e.addEventListener(t,s,n)}function rl(e,t,s,n){e.removeEventListener(t,s,n)}const Vn=Symbol("_vei");function ol(e,t,s,n,i=null){const r=e[Vn]||(e[Vn]={}),o=r[t];if(n&&o)o.value=n;else{const[l,f]=ll(t);if(n){const d=r[t]=ul(n,i);Ze(e,l,d,f)}else o&&(rl(e,l,o,f),r[t]=void 0)}}const Hn=/(?:Once|Passive|Capture)$/;function ll(e){let t;if(Hn.test(e)){t={};let n;for(;n=e.match(Hn);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):st(e.slice(2)),t]}let Ts=0;const cl=Promise.resolve(),fl=()=>Ts||(cl.then(()=>Ts=0),Ts=Date.now());function ul(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;Ae(al(n,s.value),t,5,[n])};return s.value=e,s.attached=fl(),s}function al(e,t){if(R(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>i=>!i._stopped&&n&&n(i))}else return t}const $n=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,dl=(e,t,s,n,i,r)=>{const o=i==="svg";t==="class"?Qo(e,n,o):t==="style"?nl(e,s,n):is(t)?Us(t)||ol(e,t,s,n,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):hl(e,t,n,o))?(Ln(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Dn(e,t,n,o,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!k(n))?Ln(e,Ke(t),n,r,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Dn(e,t,n,o))};function hl(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&$n(t)&&I(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return $n(t)&&k(s)?!1:t in e}const ss=e=>{const t=e.props["onUpdate:modelValue"]||!1;return R(t)?s=>zt(t,s):t};function pl(e){e.target.composing=!0}function Nn(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const gt=Symbol("_assign"),gl={created(e,{modifiers:{lazy:t,trim:s,number:n}},i){e[gt]=ss(i);const r=n||i.props&&i.props.type==="number";Ze(e,t?"change":"input",o=>{if(o.target.composing)return;let l=e.value;s&&(l=l.trim()),r&&(l=Xt(l)),e[gt](l)}),s&&Ze(e,"change",()=>{e.value=e.value.trim()}),t||(Ze(e,"compositionstart",pl),Ze(e,"compositionend",Nn),Ze(e,"change",Nn))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:n,trim:i,number:r}},o){if(e[gt]=ss(o),e.composing)return;const l=(r||e.type==="number")&&!/^0\d/.test(e.value)?Xt(e.value):e.value,f=t??"";l!==f&&(document.activeElement===e&&e.type!=="range"&&(n&&t===s||i&&e.value.trim()===f)||(e.value=f))}},Ps={deep:!0,created(e,{value:t,modifiers:{number:s}},n){const i=rs(t);Ze(e,"change",()=>{const r=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>s?Xt(ns(o)):ns(o));e[gt](e.multiple?i?new Set(r):r:r[0]),e._assigning=!0,tn(()=>{e._assigning=!1})}),e[gt]=ss(n)},mounted(e,{value:t}){jn(e,t)},beforeUpdate(e,t,s){e[gt]=ss(s)},updated(e,{value:t}){e._assigning||jn(e,t)}};function jn(e,t){const s=e.multiple,n=R(t);if(!(s&&!n&&!rs(t))){for(let i=0,r=e.options.length;i<r;i++){const o=e.options[i],l=ns(o);if(s)if(n){const f=typeof l;f==="string"||f==="number"?o.selected=t.some(d=>String(d)===String(l)):o.selected=cr(t,l)>-1}else o.selected=t.has(l);else if(cs(ns(o),t)){e.selectedIndex!==i&&(e.selectedIndex=i);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function ns(e){return"_value"in e?e._value:e.value}const ml=te({patchProp:dl},ko);let Un;function _l(){return Un||(Un=bo(ml))}const vl=(...e)=>{const t=_l().createApp(...e),{mount:s}=t;return t.mount=n=>{const i=xl(n);if(!i)return;const r=t._component;!I(r)&&!r.render&&!r.template&&(r.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const o=s(i,!1,bl(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),o},t};function bl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function xl(e){return k(e)?document.querySelector(e):e}class Gn{constructor(t){z(this,"buffer");z(this,"writeIndex",0);z(this,"config");z(this,"isBufferFull",!1);this.config=t,this.buffer=new Float32Array(t.bufferSize),this.buffer.fill(t.benchmark)}addDataPoint(t){const s=(t-this.config.benchmark)/this.config.amplitude;this.buffer[this.writeIndex]=s,this.writeIndex=(this.writeIndex+1)%this.config.bufferSize,this.writeIndex===0&&(this.isBufferFull=!0)}addDataPoints(t){for(const s of t)this.addDataPoint(s)}getWaveformData(t,s=0){const n=Math.min(t,this.getAvailableDataLength()),i=new Float32Array(n);let r=this.writeIndex-1-s;r<0&&(r+=this.config.bufferSize);let o=r-n+1;o<0&&(o+=this.config.bufferSize);for(let l=0;l<n;l++){const f=(o+l)%this.config.bufferSize;i[l]=this.buffer[f]}return{data:i,startIndex:o,endIndex:r,length:n}}getAvailableDataLength(){return this.isBufferFull?this.config.bufferSize:this.writeIndex}getLatestDataPoint(){if(this.writeIndex===0&&!this.isBufferFull)return this.config.benchmark/this.config.amplitude;const t=this.writeIndex===0?this.config.bufferSize-1:this.writeIndex-1;return this.buffer[t]}clear(){this.buffer.fill(this.config.benchmark/this.config.amplitude),this.writeIndex=0,this.isBufferFull=!1}getBufferUsage(){return this.getAvailableDataLength()/this.config.bufferSize}static calculateDisplayLength(t,s){return Math.floor(t*s)}static calculateTimeScale(t,s){return s/t}static calculateVoltageScale(t,s){return s/t}}class Wn{constructor(t,s){z(this,"canvas");z(this,"ctx");z(this,"config");z(this,"gridCanvas",null);z(this,"gridCtx",null);z(this,"lastCanvasSize",{width:0,height:0});z(this,"pixelsPerSecond",0);z(this,"pixelsPerMV",0);z(this,"smallGridPixelsX",0);z(this,"smallGridPixelsY",0);z(this,"largeGridPixelsX",0);z(this,"largeGridPixelsY",0);this.canvas=t;const n=t.getContext("2d");if(!n)throw new Error("Failed to get 2D context from canvas");this.ctx=n,this.config=s,this.updateRenderParameters()}updateRenderParameters(){const{width:t,height:s}=this.canvas;this.pixelsPerSecond=this.config.paperSpeed*this.config.speedMultiplier/25.4*96,this.pixelsPerMV=s/(20*this.config.voltageMultiplier),this.smallGridPixelsX=this.pixelsPerSecond*this.config.smallGridTime,this.smallGridPixelsY=this.pixelsPerMV*this.config.smallGridVoltage,this.largeGridPixelsX=this.pixelsPerSecond*this.config.largeGridTime,this.largeGridPixelsY=this.pixelsPerMV*this.config.largeGridVoltage}shouldRedrawGrid(){const{width:t,height:s}=this.canvas;return!this.gridCanvas||this.lastCanvasSize.width!==t||this.lastCanvasSize.height!==s}drawGrid(){const{width:t,height:s}=this.canvas;this.gridCanvas||(this.gridCanvas=document.createElement("canvas"),this.gridCtx=this.gridCanvas.getContext("2d")),this.gridCanvas.width=t,this.gridCanvas.height=s,this.lastCanvasSize={width:t,height:s};const n=this.gridCtx;n.fillStyle=this.config.backgroundColor,n.fillRect(0,0,t,s),n.strokeStyle=this.config.gridColor,n.lineWidth=.5,n.beginPath();for(let i=0;i<=t;i+=this.smallGridPixelsX)n.moveTo(i,0),n.lineTo(i,s);for(let i=0;i<=s;i+=this.smallGridPixelsY)n.moveTo(0,i),n.lineTo(t,i);n.stroke(),n.strokeStyle=this.config.largeGridColor,n.lineWidth=1,n.beginPath();for(let i=0;i<=t;i+=this.largeGridPixelsX)n.moveTo(i,0),n.lineTo(i,s);for(let i=0;i<=s;i+=this.largeGridPixelsY)n.moveTo(0,i),n.lineTo(t,i);n.stroke(),this.drawLabels(n,t,s)}drawLabels(t,s,n){t.fillStyle=this.config.textColor,t.font=`${this.config.fontSize}px ${this.config.fontFamily}`;const i=`${this.config.paperSpeed*this.config.speedMultiplier}mm/s`,r=`${this.config.voltageMultiplier}x`;t.fillText(i,10,n-30),t.fillText(r,10,n-10),this.config.largeGridTime;for(let l=0;l<=s;l+=this.largeGridPixelsX){const f=(l/this.pixelsPerSecond).toFixed(1);t.fillText(`${f}s`,l+2,15)}const o=n/2;this.config.largeGridVoltage;for(let l=0;l<=n;l+=this.largeGridPixelsY){const f=((o-l)/this.pixelsPerMV).toFixed(0);Math.abs(parseFloat(f))<.1||t.fillText(`${f}mV`,2,l-2)}}drawWaveform(t){if(this.updateRenderParameters(),this.shouldRedrawGrid()&&this.drawGrid(),this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.gridCanvas&&this.ctx.drawImage(this.gridCanvas,0,0),t.length===0)return;const{data:s}=t,{width:n,height:i}=this.canvas,r=i/2;this.ctx.strokeStyle=this.config.waveformColor,this.ctx.lineWidth=1.5,this.ctx.beginPath();const o=n/(s.length-1),l=r-s[0]*this.pixelsPerMV;this.ctx.moveTo(0,l);for(let f=1;f<s.length;f++){const d=f*o,a=r-s[f]*this.pixelsPerMV;this.ctx.lineTo(d,a)}this.ctx.stroke()}drawWaveformRealtime(t,s=-1){this.updateRenderParameters(),this.shouldRedrawGrid()&&(this.drawGrid(),this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.gridCanvas&&this.ctx.drawImage(this.gridCanvas,0,0),s=-1);const{data:n}=t;if(n.length===0)return s;const{width:i,height:r}=this.canvas,o=r/2,l=i/(n.length-1);if(this.ctx.strokeStyle=this.config.waveformColor,this.ctx.lineWidth=1.5,s<0){this.ctx.beginPath();const f=o-n[0]*this.pixelsPerMV;this.ctx.moveTo(0,f);for(let d=1;d<n.length;d++){const a=d*l,p=o-n[d]*this.pixelsPerMV;this.ctx.lineTo(a,p)}return this.ctx.stroke(),n.length-1}if(s<n.length-1){this.ctx.beginPath();const f=s*l,d=o-n[s]*this.pixelsPerMV;this.ctx.moveTo(f,d);for(let a=s+1;a<n.length;a++){const p=a*l,w=o-n[a]*this.pixelsPerMV;this.ctx.lineTo(p,w)}this.ctx.stroke()}return n.length-1}updateConfig(t){this.config={...this.config,...t},this.updateRenderParameters(),this.lastCanvasSize={width:0,height:0}}clear(){this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.gridCanvas&&this.ctx.drawImage(this.gridCanvas,0,0)}static getDefaultConfig(){return{smallGridTime:.04,largeGridTime:.2,smallGridVoltage:1,largeGridVoltage:5,paperSpeed:50,speedMultiplier:1,voltageMultiplier:1,backgroundColor:"#000000",gridColor:"#003300",largeGridColor:"#006600",waveformColor:"#00ff00",textColor:"#ffffff",fontSize:12,fontFamily:"Arial, sans-serif"}}}class yl{constructor(){z(this,"frameCount",0);z(this,"lastTime",performance.now());z(this,"renderTimes",[]);z(this,"dataProcessingTimes",[]);z(this,"maxSamples",60)}startRenderTiming(){const t=performance.now();return()=>{const n=performance.now()-t;this.renderTimes.push(n),this.renderTimes.length>this.maxSamples&&this.renderTimes.shift()}}startDataProcessingTiming(){const t=performance.now();return()=>{const n=performance.now()-t;this.dataProcessingTimes.push(n),this.dataProcessingTimes.length>this.maxSamples&&this.dataProcessingTimes.shift()}}updateFPS(){this.frameCount++;const t=performance.now();t-this.lastTime>=1e3&&(this.lastTime=t,this.frameCount=0)}getFPS(){const s=performance.now()-this.lastTime;return s<1e3?Math.round(this.frameCount*1e3/s):0}getAverageRenderTime(){return this.renderTimes.length===0?0:this.renderTimes.reduce((s,n)=>s+n,0)/this.renderTimes.length}getAverageDataProcessingTime(){return this.dataProcessingTimes.length===0?0:this.dataProcessingTimes.reduce((s,n)=>s+n,0)/this.dataProcessingTimes.length}getMemoryUsage(){return"memory"in performance?performance.memory.usedJSHeapSize/1024/1024:0}getMetrics(){return{fps:this.getFPS(),renderTime:this.getAverageRenderTime(),dataProcessingTime:this.getAverageDataProcessingTime(),memoryUsage:this.getMemoryUsage(),cpuUsage:0}}reset(){this.frameCount=0,this.lastTime=performance.now(),this.renderTimes=[],this.dataProcessingTimes=[]}}const Qe=class Qe{constructor(){z(this,"gcTimer",null)}static getInstance(){return Qe.instance||(Qe.instance=new Qe),Qe.instance}startPeriodicGC(t=3e4){this.stopPeriodicGC(),this.gcTimer=window.setInterval(()=>{this.suggestGC()},t)}stopPeriodicGC(){this.gcTimer&&(clearInterval(this.gcTimer),this.gcTimer=null)}suggestGC(){"gc"in window&&typeof window.gc=="function"&&window.gc()}checkMemoryPressure(){if("memory"in performance){const t=performance.memory,s=t.usedJSHeapSize/t.jsHeapSizeLimit;return s>.8?"high":s>.6?"medium":"low"}return"low"}};z(Qe,"instance");let Ns=Qe;const Sl={class:"ecg-display"},wl={class:"controls"},Cl={class:"control-group"},Tl={key:0,class:"control-group"},Pl=["disabled"],El={key:1,class:"control-group"},Ml=["disabled"],Rl=["disabled"],Al={class:"control-group"},Ol={class:"control-group"},Il={class:"status"},Fl={key:0},Dl={key:1},Ll=["width","height"],Vl=vi({__name:"ECGDisplay",setup(e){const t=de(),s=de(),n=de(1200),i=de(600),r=de("file"),o=de("http://localhost:8081/sse"),l=de(!1),f=de(!1),d=de("就绪"),a=de(0),p=de(0),w=de(1),T=de(1);let A=null,F=null,J=null,L=-1,$=null;const K=new yl,M=Ns.getInstance();Si(async()=>{await tn(),q(),be(),M.startPeriodicGC(3e4),window.addEventListener("resize",oe),oe()}),rn(()=>{Z(),M.stopPeriodicGC(),window.removeEventListener("resize",oe)});function q(){if(!t.value)return;const D={bufferSize:5e4,sampleRate:500,benchmark:2048,amplitude:800};A=new Gn(D);const C=Wn.getDefaultConfig();C.speedMultiplier=w.value,C.voltageMultiplier=T.value,F=new Wn(t.value,C),d.value="已初始化"}function be(){function D(){const C=K.startRenderTiming();if(A&&F){const X=Gn.calculateDisplayLength(10,500),me=A.getWaveformData(X);me.length>0&&(L=F.drawWaveformRealtime(me,L),a.value=A.getAvailableDataLength(),p.value=A.getBufferUsage())}C(),K.updateFPS(),J=requestAnimationFrame(D)}D()}function oe(){if(!s.value)return;const D=s.value.getBoundingClientRect();n.value=D.width,i.value=D.height,L=-1}function xe(){L=-1}function nt(){r.value==="sse"&&f.value&&Ie(),d.value=`切换到${r.value==="file"?"CSV文件":"SSE推送"}模式`}async function Oe(){if(!(!window.electronAPI||!A)){l.value=!0,d.value="加载文件中...";try{const D=await window.electronAPI.getDataPath(),C=await window.electronAPI.readCsvFile(`${D}/waveform_714.csv`);if(C.success&&C.data){const X=C.data.trim().split(`
`),me=[];for(const Fe of X){const ue=Fe.split(","),ot=ue[ue.length-1];ot&&!isNaN(Number(ot))&&me.push(Number(ot))}A.clear(),L=-1,A.addDataPoints(me),d.value=`已加载 ${me.length} 个数据点`}else d.value=`加载失败: ${C.error}`}catch(D){d.value=`加载错误: ${D}`}finally{l.value=!1}}}function Ye(){f.value?Ie():it()}function it(){if(!(!o.value||!A)){l.value=!0,d.value="连接SSE中...";try{$=new EventSource(o.value),$.onopen=()=>{f.value=!0,l.value=!1,d.value="SSE已连接"},$.onmessage=D=>{try{const C=JSON.parse(D.data);typeof C=="number"?A.addDataPoint(C):Array.isArray(C)&&A.addDataPoints(C)}catch(C){console.error("解析SSE数据失败:",C)}},$.onerror=()=>{d.value="SSE连接错误",Ie()}}catch(D){d.value=`SSE连接失败: ${D}`,l.value=!1}}}function Ie(){$&&($.close(),$=null),f.value=!1,d.value="SSE已断开"}function rt(){F&&(F.updateConfig({speedMultiplier:w.value,voltageMultiplier:T.value}),L=-1)}function Nt(){A&&A.clear(),F&&F.clear(),L=-1,a.value=0,p.value=0,d.value="显示已清空"}function Z(){J&&cancelAnimationFrame(J),Ie()}return(D,C)=>(ke(),ut("div",Sl,[H("div",wl,[H("div",Cl,[C[5]||(C[5]=H("label",null,"数据源:",-1)),Bt(H("select",{"onUpdate:modelValue":C[0]||(C[0]=X=>r.value=X),onChange:nt},C[4]||(C[4]=[H("option",{value:"file"},"CSV文件",-1),H("option",{value:"sse"},"SSE推送",-1)]),544),[[Ps,r.value]])]),r.value==="file"?(ke(),ut("div",Tl,[H("button",{onClick:Oe,disabled:l.value},ct(l.value?"加载中...":"加载CSV文件"),9,Pl)])):Kt("",!0),r.value==="sse"?(ke(),ut("div",El,[Bt(H("input",{"onUpdate:modelValue":C[1]||(C[1]=X=>o.value=X),placeholder:"SSE URL",disabled:f.value},null,8,Ml),[[gl,o.value]]),H("button",{onClick:Ye,disabled:l.value},ct(f.value?"断开连接":"连接SSE"),9,Rl)])):Kt("",!0),H("div",Al,[C[7]||(C[7]=H("label",null,"走纸速度:",-1)),Bt(H("select",{"onUpdate:modelValue":C[2]||(C[2]=X=>w.value=X),onChange:rt},C[6]||(C[6]=[H("option",{value:.25},"12.5mm/s",-1),H("option",{value:.5},"25mm/s",-1),H("option",{value:1},"50mm/s",-1),H("option",{value:2},"100mm/s",-1)]),544),[[Ps,w.value]])]),H("div",Ol,[C[9]||(C[9]=H("label",null,"电压倍数:",-1)),Bt(H("select",{"onUpdate:modelValue":C[3]||(C[3]=X=>T.value=X),onChange:rt},C[8]||(C[8]=[H("option",{value:.25},"x0.25",-1),H("option",{value:.5},"x0.5",-1),H("option",{value:1},"x1",-1),H("option",{value:2},"x2",-1)]),544),[[Ps,T.value]])]),H("div",{class:"control-group"},[H("button",{onClick:Nt},"清空显示")]),H("div",Il,[H("span",null,"状态: "+ct(d.value),1),a.value>0?(ke(),ut("span",Fl,"数据点: "+ct(a.value),1)):Kt("",!0),p.value>0?(ke(),ut("span",Dl,"缓冲区: "+ct((p.value*100).toFixed(1))+"%",1)):Kt("",!0)])]),H("div",{class:"canvas-container",ref_key:"canvasContainer",ref:s},[H("canvas",{ref_key:"canvas",ref:t,onResize:xe,width:n.value,height:i.value},null,40,Ll)],512)]))}}),Hl=(e,t)=>{const s=e.__vccOpts||e;for(const[n,i]of t)s[n]=i;return s},$l=Hl(Vl,[["__scopeId","data-v-033fc7de"]]),Nl={id:"app"},jl=vi({__name:"App",setup(e){return(t,s)=>(ke(),ut("div",Nl,[Me($l)]))}});vl(jl).mount("#app");
