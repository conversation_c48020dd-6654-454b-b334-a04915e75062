"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    readCsvFile: (filePath) => electron_1.ipcRenderer.invoke('read-csv-file', filePath),
    getDataPath: () => electron_1.ipcRenderer.invoke('get-data-path'),
});
