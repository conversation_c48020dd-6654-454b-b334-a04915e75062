{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../../electron/main.ts"], "names": [], "mappings": ";;AAAA,uCAAsD;AACtD,+BAA2B;AAC3B,0CAAsC;AAEtC,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAA;AAEpD,SAAS,YAAY;IACnB,MAAM,UAAU,GAAG,IAAI,wBAAa,CAAC;QACnC,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,GAAG;QACX,cAAc,EAAE;YACd,eAAe,EAAE,KAAK;YACtB,gBAAgB,EAAE,IAAI;YACtB,OAAO,EAAE,IAAA,WAAI,EAAC,SAAS,EAAE,YAAY,CAAC;SACvC;KACF,CAAC,CAAA;IAEF,IAAI,KAAK,EAAE,CAAC;QACV,UAAU,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAA;QAC3C,UAAU,CAAC,WAAW,CAAC,YAAY,EAAE,CAAA;IACvC,CAAC;SAAM,CAAC;QACN,UAAU,CAAC,QAAQ,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,CAAA;IAC3D,CAAC;AACH,CAAC;AAED,cAAG,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;IACxB,YAAY,EAAE,CAAA;IAEd,cAAG,CAAC,EAAE,CAAC,UAAU,EAAE;QACjB,IAAI,wBAAa,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,CAAC;YAAE,YAAY,EAAE,CAAA;IAChE,CAAC,CAAC,CAAA;AACJ,CAAC,CAAC,CAAA;AAEF,cAAG,CAAC,EAAE,CAAC,mBAAmB,EAAE;IAC1B,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ;QAAE,cAAG,CAAC,IAAI,EAAE,CAAA;AAC/C,CAAC,CAAC,CAAA;AAEF,mCAAmC;AACnC,kBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,QAAgB,EAAE,EAAE;IACjE,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,IAAA,mBAAQ,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QAC9C,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;IAChC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,CAAA;IAC5D,CAAC;AACH,CAAC,CAAC,CAAA;AAEF,kBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,EAAE;IACnC,OAAO,IAAA,WAAI,EAAC,SAAS,EAAE,YAAY,CAAC,CAAA;AACtC,CAAC,CAAC,CAAA"}