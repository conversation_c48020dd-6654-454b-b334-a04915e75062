#!/bin/bash

# ECG Display 构建脚本
echo "🔧 开始构建 ECG Display 应用..."

# 检查 Node.js 和 npm
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo "✅ npm 版本: $(npm --version)"

# 安装依赖
echo "📦 安装依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

# 构建 TypeScript 文件
echo "🔨 编译 TypeScript 文件..."
mkdir -p dist/electron

# 编译 Electron 主进程
npx tsc electron/main.ts --outDir dist/electron --target ES2020 --module commonjs --esModuleInterop --skipLibCheck
if [ $? -ne 0 ]; then
    echo "❌ main.ts 编译失败"
    exit 1
fi

# 编译 Electron 预加载脚本
npx tsc electron/preload.ts --outDir dist/electron --target ES2020 --module commonjs --esModuleInterop --skipLibCheck
if [ $? -ne 0 ]; then
    echo "❌ preload.ts 编译失败"
    exit 1
fi

# 构建 Vue 应用
echo "🏗️ 构建 Vue 应用..."
npm run build:vue

if [ $? -ne 0 ]; then
    echo "❌ Vue 应用构建失败"
    exit 1
fi

echo "✅ 构建完成!"
echo ""
echo "📋 构建结果:"
echo "- Electron 主进程: dist/electron/main.js"
echo "- Electron 预加载: dist/electron/preload.js"
echo "- Vue 应用: dist/vue/"
echo ""
echo "🚀 启动应用:"
echo "开发模式: npm run dev"
echo "生产模式: npm start"
