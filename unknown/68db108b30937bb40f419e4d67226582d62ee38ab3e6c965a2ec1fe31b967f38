{"version": 3, "file": "RuleCreator.js", "sourceRoot": "", "sources": ["../../src/eslint-utils/RuleCreator.ts"], "names": [], "mappings": ";;;AAOA,iDAA8C;AAuC9C;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,UAAwC;IAClE,oHAAoH;IACpH,2FAA2F;IAC3F,OAAO,SAAS,eAAe,CAG7B,EACA,IAAI,EACJ,IAAI,EACJ,GAAG,IAAI,EAC8C;QAIrD,OAAO,UAAU,CAAwB;YACvC,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,IAAI,EAAE;oBACJ,GAAG,IAAI,CAAC,IAAI;oBACZ,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;iBACtB;aACF;YACD,GAAG,IAAI;SACR,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAzBD,kCAyBC;AAED;;;;;GAKG;AACH,SAAS,UAAU,CAGjB,EACA,MAAM,EACN,cAAc,EACd,IAAI,GAC0C;IAI9C,OAAO;QACL,MAAM,CACJ,OAAqD;YAErD,MAAM,kBAAkB,GAAG,IAAA,2BAAY,EAAC,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YACzE,OAAO,MAAM,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC7C,CAAC;QACD,cAAc;QACd,IAAI;KACL,CAAC;AACJ,CAAC;AAED,WAAW,CAAC,WAAW,GAAG,UAAU,CAAC"}