/**
 * 简单的SSE测试服务器
 * 用于测试ECG波形显示的SSE功能
 */

const http = require('http')
const fs = require('fs')
const path = require('path')

const PORT = 8081

// 读取CSV数据作为测试数据
let testData = []
try {
  const csvPath = path.join(__dirname, 'data', 'waveform_714.csv')
  const csvContent = fs.readFileSync(csvPath, 'utf-8')
  const lines = csvContent.trim().split('\n')
  
  testData = lines.map(line => {
    const columns = line.split(',')
    const lastColumn = columns[columns.length - 1]
    return lastColumn && !isNaN(Number(lastColumn)) ? Number(lastColumn) : 2048
  })
  
  console.log(`Loaded ${testData.length} data points from CSV`)
} catch (error) {
  console.error('Failed to load CSV data:', error.message)
  // 生成模拟数据
  for (let i = 0; i < 1000; i++) {
    testData.push(2048 + Math.sin(i * 0.1) * 100 + Math.random() * 20)
  }
  console.log('Using simulated data')
}

const server = http.createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

  if (req.method === 'OPTIONS') {
    res.writeHead(200)
    res.end()
    return
  }

  if (req.url === '/sse' && req.method === 'GET') {
    // SSE连接
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    })

    // 发送连接确认
    res.write('event: connect\n')
    res.write('data: {"status": "connected"}\n\n')

    let dataIndex = 0
    let clientConnected = true

    // 定期发送数据
    const dataInterval = setInterval(() => {
      if (!clientConnected) {
        clearInterval(dataInterval)
        return
      }

      // 发送单个数据点
      const value = testData[dataIndex % testData.length]
      res.write('event: data\n')
      res.write(`data: ${value}\n\n`)
      
      dataIndex++

      // 每10个数据点发送一次批量数据
      if (dataIndex % 10 === 0) {
        const batchData = []
        for (let i = 0; i < 5; i++) {
          batchData.push(testData[(dataIndex + i) % testData.length])
        }
        res.write('event: data\n')
        res.write(`data: ${JSON.stringify(batchData)}\n\n`)
        dataIndex += 5
      }
    }, 20) // 50Hz (每20ms一个数据点)

    // 心跳
    const heartbeatInterval = setInterval(() => {
      if (!clientConnected) {
        clearInterval(heartbeatInterval)
        return
      }
      
      res.write('event: heartbeat\n')
      res.write(`data: ${Date.now()}\n\n`)
    }, 30000) // 30秒心跳

    // 客户端断开连接处理
    req.on('close', () => {
      clientConnected = false
      clearInterval(dataInterval)
      clearInterval(heartbeatInterval)
      console.log('Client disconnected')
    })

    console.log('SSE client connected')
  } else if (req.url === '/status' && req.method === 'GET') {
    // 状态接口
    res.writeHead(200, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({
      status: 'running',
      dataPoints: testData.length,
      timestamp: new Date().toISOString()
    }))
  } else {
    // 404
    res.writeHead(404, { 'Content-Type': 'text/plain' })
    res.end('Not Found')
  }
})

server.listen(PORT, () => {
  console.log(`SSE Test Server running on http://localhost:${PORT}`)
  console.log(`SSE endpoint: http://localhost:${PORT}/sse`)
  console.log(`Status endpoint: http://localhost:${PORT}/status`)
  console.log('Press Ctrl+C to stop')
})

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\nShutting down server...')
  server.close(() => {
    console.log('Server closed')
    process.exit(0)
  })
})
