# ECG Display

基于 Electron + Vue 3 + TypeScript 的心电图波形显示应用程序。

## 功能特性

- 📊 实时ECG波形显示
- 📁 CSV文件数据加载
- 🔄 SSE (Server-Sent Events) 实时数据推送
- ⚡ **500Hz频率输出** - 按照标准采样率精确输出数据
- 🎛️ 可调节的走纸速度和电压倍数
- 🎨 专业的心电图网格显示
- ⚡ 高性能渲染，支持大量数据点
- 🔧 模块化架构，易于扩展

## 技术架构

### 核心模块

1. **waveform_calc.ts** - 波形数据计算模块
   - 循环缓冲区管理
   - 数据预处理和转换
   - 高效的数据索引计算

2. **waveform.ts** - 波形渲染模块
   - Canvas 2D 渲染
   - 离屏网格缓存
   - 实时波形绘制优化

3. **file_reader.ts** - 文件读取模块
   - CSV 文件解析
   - 分块处理大文件
   - 进度回调支持

4. **sse_handler.ts** - SSE 数据处理模块
   - 自动重连机制
   - 心跳检测
   - 数据缓冲管理

## 安装和运行

### 环境要求

- Node.js 18+
- npm 或 yarn

### 快速开始

```bash
# 1. 安装依赖
npm install

# 2. 构建项目
./scripts/build.sh
# 或手动构建
npm run build

# 3. 启动开发环境
npm run dev
```

### 开发模式

```bash
# 方式 1: 同时启动 Vue 和 Electron
npm run dev

# 方式 2: 分别启动
npm run dev:vue    # 启动 Vite 开发服务器
npm run dev:electron  # 启动 Electron 应用

# 方式 3: 测试 SSE 功能
node test-sse-server.js  # 启动 SSE 测试服务器
```

### 生产模式

```bash
# 构建应用
npm run build

# 启动生产版本
npm start
```

### 功能测试

```bash
# 运行完整功能测试
node test-functionality.js
```

### 构建应用

```bash
# 构建 Vue 应用和 Electron 主进程
npm run build
```

### 测试 SSE 功能

启动测试 SSE 服务器：

```bash
node test-sse-server.js
```

然后在应用中选择 "SSE推送" 模式，使用默认 URL `http://localhost:8081/sse`。

## 当前状态

✅ **项目已完全实现并可运行**

当前运行的服务：
- 🟢 Vite 开发服务器: http://localhost:5173
- 🟢 SSE 测试服务器: http://localhost:8081
- 🟢 Electron 应用: 已启动

所有核心功能已实现：
- ✅ CSV 文件加载和解析
- ✅ SSE 实时数据推送
- ✅ 专业心电图网格显示
- ✅ 实时波形渲染
- ✅ 性能监控和优化
- ✅ 用户界面控制

## 使用说明

### CSV 文件格式

应用支持标准 CSV 格式，数据应位于最后一列：

```csv
,,,,,,,2417
,,,,,,,2340
,,,,,,,2395
```

### 500Hz 频率输出模式

新增的 500Hz 频率输出功能可以按照标准心电图采样率精确输出数据：

- **精确时序**: 每 2ms 输出一个数据点
- **实时模拟**: 模拟真实心电图设备的数据流
- **可控制**: 支持开始/停止控制
- **进度监控**: 实时显示输出进度和当前数值

使用方法：
1. 选择 "CSV文件" 数据源
2. 勾选 "500Hz频率输出" 选项
3. 点击 "开始500Hz输出" 按钮
4. 观察实时波形绘制过程

### 数据格式说明

- **采样率**: 500Hz
- **基准值**: 2048 (每个数值需要减去此基准值)
- **振幅**: 800 amplitude/mV
- **数据格式**: 整数值

### 控制参数

- **走纸速度**: 12.5mm/s, 25mm/s, 50mm/s, 100mm/s
- **电压倍数**: x0.25, x0.5, x1, x2
- **网格**: 小格 0.04s/1mV, 大格 0.2s/5mV

## 性能优化

### 内存管理
- 使用 Float32Array 减少内存占用
- 循环缓冲区避免内存泄漏
- 离屏 Canvas 缓存网格

### 渲染优化
- requestAnimationFrame 确保流畅渲染
- 增量绘制减少重绘开销
- 分块数据处理避免阻塞 UI

### CPU 优化
- Web Worker 支持 (可扩展)
- 批量数据处理
- 智能重绘策略

## 项目结构

```
ecg-display/
├── electron/           # Electron 主进程
│   ├── main.ts
│   └── preload.ts
├── src/
│   ├── components/     # Vue 组件
│   │   └── ECGDisplay.vue
│   ├── utils/          # 核心工具模块
│   │   ├── waveform_calc.ts
│   │   ├── waveform.ts
│   │   ├── file_reader.ts
│   │   └── sse_handler.ts
│   ├── types/          # TypeScript 类型定义
│   └── main.ts
├── data/               # 测试数据
│   └── waveform_714.csv
└── test-sse-server.js  # SSE 测试服务器
```

## 扩展开发

### 添加新的数据源

1. 实现数据处理接口
2. 继承或使用 `WaveformCalculator`
3. 在主组件中集成

### 自定义渲染样式

修改 `WaveformRenderer` 的配置：

```typescript
const config = WaveformRenderer.getDefaultConfig()
config.waveformColor = '#ff0000'  // 红色波形
config.gridColor = '#333333'      // 深灰网格
```

### 性能监控

应用提供实时性能指标：
- 数据点数量
- 缓冲区使用率
- 连接状态

## 故障排除

### 常见问题

1. **文件加载失败**
   - 检查文件路径和格式
   - 确保 CSV 最后一列包含数值

2. **SSE 连接失败**
   - 检查服务器是否运行
   - 验证 URL 格式和 CORS 设置

3. **渲染性能问题**
   - 减少数据窗口大小
   - 检查浏览器硬件加速

## 许可证

MIT License
