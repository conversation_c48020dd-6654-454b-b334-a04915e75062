# 500Hz 频率输出功能指南

## 🎯 功能概述

新增的 500Hz 频率输出功能允许按照标准心电图采样率（500Hz）精确输出 CSV 数据，模拟真实心电图设备的数据流。

## ⚡ 技术特性

### 精确时序控制
- **输出频率**: 500Hz (每秒 500 个数据点)
- **时间间隔**: 2ms 精确间隔
- **时序优化**: 使用 `performance.now()` 进行高精度时间控制
- **累积误差补偿**: 自动调整延迟以保持长期精度

### 实时监控
- **进度显示**: 实时显示输出进度 (当前/总数)
- **数值监控**: 显示当前输出的数据值
- **状态反馈**: 详细的状态信息和错误处理
- **性能统计**: 缓冲区使用率和数据点计数

### 用户控制
- **开始/停止**: 随时控制输出过程
- **模式切换**: 可选择传统批量加载或频率输出
- **清空功能**: 自动停止输出并清空数据

## 🚀 使用方法

### 1. 启动应用
```bash
npm run dev
```

### 2. 选择频率输出模式
1. 在数据源选择中选择 "CSV文件"
2. 勾选 "500Hz频率输出" 复选框
3. 点击 "开始500Hz输出" 按钮

### 3. 监控输出过程
- 观察状态栏显示的进度信息
- 查看实时波形绘制
- 监控数据点计数和缓冲区使用率

### 4. 控制输出
- 点击 "停止输出" 按钮可随时停止
- 点击 "清空显示" 会停止输出并清空数据

## 📊 数据处理流程

```
CSV文件 → 解析数据 → 500Hz定时器 → 波形计算器 → 实时渲染
   ↓           ↓           ↓            ↓           ↓
读取文件    提取数值    精确时序     循环缓冲区    Canvas绘制
```

### 详细步骤

1. **文件读取**: 使用 Electron IPC 读取 CSV 文件
2. **数据解析**: 提取最后一列的数值数据
3. **频率输出**: 按 2ms 间隔逐个输出数据点
4. **数据处理**: 应用基准值(2048)和振幅(800)转换
5. **实时渲染**: 使用 requestAnimationFrame 流畅绘制

## 🔧 技术实现

### 核心算法
```typescript
// 精确时序控制
let nextScheduledTime = startTime
const outputNextValue = () => {
  // 输出数据点
  waveformCalculator.addDataPoint(value)
  
  // 计算下次输出时间
  nextScheduledTime += intervalMs
  const currentTime = performance.now()
  const delay = Math.max(0, nextScheduledTime - currentTime)
  
  // 设置精确延迟
  setTimeout(outputNextValue, delay)
}
```

### 性能优化
- **高精度计时**: 使用 `performance.now()` 替代 `Date.now()`
- **累积误差补偿**: 动态调整延迟时间
- **内存管理**: 循环缓冲区避免内存泄漏
- **渲染优化**: 增量绘制减少重绘开销

## 📈 测试验证

### 频率精度测试
运行测试脚本验证输出频率：
```bash
node test-frequency-output.js
```

### 预期结果
- **目标频率**: 500Hz
- **实际频率**: 约 450-500Hz (受系统性能影响)
- **数据完整性**: 100% (所有数据点都会输出)
- **时序稳定性**: 长期运行保持稳定

## 🎛️ 配置选项

### FileReaderConfig
```typescript
interface FileReaderConfig {
  outputFrequency: number // 输出频率，默认 500Hz
  chunkSize: number       // 处理块大小
  delimiter: string       // CSV 分隔符
  targetColumn: number    // 目标列索引
  skipEmptyLines: boolean // 跳过空行
  skipHeader: boolean     // 跳过标题行
}
```

### 自定义频率
可以修改配置来使用不同的输出频率：
```typescript
const config = FileReader.getDefaultConfig()
config.outputFrequency = 250 // 250Hz
config.outputFrequency = 1000 // 1000Hz
```

## 🔍 故障排除

### 常见问题

1. **频率不准确**
   - 原因: 系统负载过高或浏览器限制
   - 解决: 关闭其他应用，使用性能模式

2. **输出中断**
   - 原因: 内存不足或数据错误
   - 解决: 检查 CSV 文件格式，重启应用

3. **波形不流畅**
   - 原因: 渲染性能不足
   - 解决: 降低显示窗口大小，关闭开发者工具

### 性能建议

- **最佳性能**: 关闭浏览器开发者工具
- **稳定输出**: 避免在输出过程中进行其他操作
- **内存管理**: 定期清空显示以释放内存

## 📋 应用场景

### 医疗设备模拟
- 模拟真实心电图设备的数据输出
- 测试数据采集系统的实时性能
- 验证波形显示算法的准确性

### 教学演示
- 展示心电图数据的实时采集过程
- 演示不同采样率对波形质量的影响
- 教学心电图信号处理原理

### 系统测试
- 测试实时数据处理能力
- 验证系统在高频数据下的稳定性
- 评估渲染性能和内存使用

## 🎉 总结

500Hz 频率输出功能为 ECG 显示应用增加了重要的实时数据模拟能力，使其更接近真实的医疗设备工作模式。通过精确的时序控制和优化的性能设计，该功能能够稳定、准确地输出大量心电图数据，为医疗设备开发、教学演示和系统测试提供了强大的工具。
