"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_collection = void 0;
const base_config_1 = require("./base-config");
exports.esnext_collection = {
    WeakKeyTypes: base_config_1.TYPE,
};
//# sourceMappingURL=esnext.collection.js.map