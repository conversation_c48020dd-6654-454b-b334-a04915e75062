{"name": "ecg-display", "version": "1.0.0", "description": "ECG waveform display application using Electron + Vue + TypeScript", "main": "dist/electron/main.js", "scripts": {"dev": "concurrently \"npm run dev:vue\" \"npm run dev:electron\"", "dev:vue": "vite", "dev:electron": "wait-on http://localhost:5173 && electron .", "build": "npm run build:vue && npm run build:electron", "build:vue": "vite build", "build:electron": "tsc -p tsconfig.electron.json", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "keywords": ["electron", "vue", "typescript", "ecg", "waveform"], "author": "ECG Display Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "concurrently": "^8.2.2", "electron": "^28.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "typescript": "~5.3.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25", "wait-on": "^7.2.0"}, "dependencies": {"defer-to-connect": "^2.0.1", "fs-extra": "^11.3.0", "vue": "^3.3.11"}}