/**
 * 测试 500Hz 频率输出功能
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 测试 500Hz 频率输出功能...\n')

// 模拟 WaveformCalculator 类
class MockWaveformCalculator {
  constructor() {
    this.dataPoints = []
    this.startTime = null
  }

  addDataPoint(value) {
    if (!this.startTime) {
      this.startTime = Date.now()
    }
    
    this.dataPoints.push({
      value,
      timestamp: Date.now()
    })
  }

  clear() {
    this.dataPoints = []
    this.startTime = null
  }

  getStats() {
    if (this.dataPoints.length < 2) {
      return { count: this.dataPoints.length, avgInterval: 0, actualFrequency: 0 }
    }

    const totalTime = this.dataPoints[this.dataPoints.length - 1].timestamp - this.dataPoints[0].timestamp
    const avgInterval = totalTime / (this.dataPoints.length - 1)
    const actualFrequency = 1000 / avgInterval // Hz

    return {
      count: this.dataPoints.length,
      avgInterval: avgInterval.toFixed(2),
      actualFrequency: actualFrequency.toFixed(2),
      totalTime: totalTime
    }
  }
}

// 模拟 FileReader 配置和功能
const config = {
  chunkSize: 1000,
  delimiter: ',',
  targetColumn: -1,
  skipEmptyLines: true,
  skipHeader: false,
  outputFrequency: 500 // 500Hz
}

// 测试数据
const testData = []
for (let i = 0; i < 1000; i++) {
  testData.push(2048 + Math.sin(i * 0.1) * 100 + Math.random() * 20)
}

console.log(`📊 测试数据: ${testData.length} 个数据点`)
console.log(`🎯 目标频率: ${config.outputFrequency}Hz`)
console.log(`⏱️  预期间隔: ${1000 / config.outputFrequency}ms`)
console.log(`⏳ 预期总时间: ${(testData.length / config.outputFrequency).toFixed(2)}秒\n`)

// 模拟频率输出功能
function simulateFrequencyOutput(data, frequency, onProgress, onComplete) {
  const calculator = new MockWaveformCalculator()
  const intervalMs = 1000 / frequency
  let currentIndex = 0

  console.log(`🚀 开始模拟 ${frequency}Hz 频率输出...`)
  
  const startTime = Date.now()

  return new Promise((resolve) => {
    const outputNextValue = () => {
      if (currentIndex >= data.length) {
        const endTime = Date.now()
        const stats = calculator.getStats()
        
        console.log('\n📈 输出统计:')
        console.log(`   数据点数: ${stats.count}`)
        console.log(`   实际总时间: ${(endTime - startTime)}ms`)
        console.log(`   平均间隔: ${stats.avgInterval}ms`)
        console.log(`   实际频率: ${stats.actualFrequency}Hz`)
        console.log(`   频率误差: ${Math.abs(frequency - parseFloat(stats.actualFrequency)).toFixed(2)}Hz`)
        
        if (onComplete) {
          onComplete()
        }
        resolve(calculator)
        return
      }

      const value = data[currentIndex]
      calculator.addDataPoint(value)

      if (onProgress && currentIndex % 100 === 0) {
        onProgress(currentIndex + 1, data.length, value)
      }

      currentIndex++
      setTimeout(outputNextValue, intervalMs)
    }

    outputNextValue()
  })
}

// 运行测试
async function runTest() {
  try {
    const calculator = await simulateFrequencyOutput(
      testData,
      config.outputFrequency,
      (current, total, value) => {
        const progress = (current / total * 100).toFixed(1)
        process.stdout.write(`\r⏳ 进度: ${progress}% (${current}/${total}) 当前值: ${value.toFixed(0)}`)
      },
      () => {
        console.log('\n✅ 频率输出完成!')
      }
    )

    // 验证结果
    const stats = calculator.getStats()
    const targetFrequency = config.outputFrequency
    const actualFrequency = parseFloat(stats.actualFrequency)
    const frequencyError = Math.abs(targetFrequency - actualFrequency)
    const errorPercentage = (frequencyError / targetFrequency * 100).toFixed(2)

    console.log('\n🔍 测试结果验证:')
    
    if (frequencyError < 5) {
      console.log(`✅ 频率精度测试通过 (误差: ${frequencyError.toFixed(2)}Hz, ${errorPercentage}%)`)
    } else {
      console.log(`❌ 频率精度测试失败 (误差: ${frequencyError.toFixed(2)}Hz, ${errorPercentage}%)`)
    }

    if (stats.count === testData.length) {
      console.log(`✅ 数据完整性测试通过 (${stats.count}/${testData.length})`)
    } else {
      console.log(`❌ 数据完整性测试失败 (${stats.count}/${testData.length})`)
    }

    console.log('\n🎉 500Hz 频率输出功能测试完成!')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 检查 CSV 文件
console.log('📁 检查 CSV 数据文件...')
const csvPath = path.join(__dirname, 'data', 'waveform_714.csv')
if (fs.existsSync(csvPath)) {
  const csvContent = fs.readFileSync(csvPath, 'utf-8')
  const lines = csvContent.trim().split('\n')
  console.log(`✅ CSV 文件存在: ${lines.length} 行数据`)
  console.log(`⏱️  按 500Hz 输出需要时间: ${(lines.length / 500).toFixed(2)} 秒\n`)
} else {
  console.log('❌ CSV 文件不存在\n')
}

runTest()
